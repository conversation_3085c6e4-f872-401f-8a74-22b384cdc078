#!/usr/bin/env python3
"""
Convert LAS file to PCD format with coordinate reprojection.
This script converts from EPSG:4326 to EPSG:32633 and outputs to PCD format.
Uses laspy for reading LAS files and open3d for writing PCD files.
"""

import laspy
import open3d as o3d
import sys
import os
from pathlib import Path
import numpy as np
from pyproj import Transformer

def convert_las_to_pcd_with_reprojection(input_las_path: str, output_pcd_path: str,
                                       in_srs: str = "EPSG:32633",
                                       out_srs: str = "EPSG:32633") -> bool:
    """
    Convert LAS file to PCD format with coordinate reprojection using laspy and open3d.

    Args:
        input_las_path: Path to input LAS file
        output_pcd_path: Path to output PCD file
        in_srs: Input spatial reference system (default: EPSG:4326)
        out_srs: Output spatial reference system (default: EPSG:32633)

    Returns:
        bool: True if conversion successful, False otherwise
    """

    # Check if input file exists
    if not os.path.exists(input_las_path):
        print(f"Error: Input file {input_las_path} does not exist")
        return False

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_pcd_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)

    print(f"Converting {input_las_path} to {output_pcd_path}")
    print(f"Reprojecting from {in_srs} to {out_srs}")

    try:
        # Read LAS file
        print("Reading LAS file...")
        las_file = laspy.read(input_las_path)

        # Extract coordinates
        x = las_file.x
        y = las_file.y
        z = las_file.z

        print(f"Number of points in LAS file: {len(x)}")
        print(f"Original coordinate bounds:")
        print(f"  X: {x.min():.6f} to {x.max():.6f}")
        print(f"  Y: {y.min():.6f} to {y.max():.6f}")
        print(f"  Z: {z.min():.6f} to {z.max():.6f}")

        # Check if coordinate transformation is needed
        if in_srs != out_srs:
            # Set up coordinate transformation
            print(f"Setting up coordinate transformation from {in_srs} to {out_srs}")
            transformer = Transformer.from_crs(in_srs, out_srs, always_xy=True)

            # Transform coordinates
            print("Transforming coordinates...")
            x_transformed, y_transformed = transformer.transform(x, y)

            print(f"Transformed coordinate bounds:")
            print(f"  X: {x_transformed.min():.6f} to {x_transformed.max():.6f}")
            print(f"  Y: {y_transformed.min():.6f} to {y_transformed.max():.6f}")
            print(f"  Z: {z.min():.6f} to {z.max():.6f}")
        else:
            print(f"No coordinate transformation needed (input and output CRS are the same: {in_srs})")
            x_transformed, y_transformed = x, y

        # Create point cloud array
        points = np.column_stack((x_transformed, y_transformed, z))

        # Create Open3D point cloud
        print("Creating Open3D point cloud...")
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)

        # Add intensity information if available
        if hasattr(las_file, 'intensity'):
            # Normalize intensity to [0, 1] range for colors
            intensity = las_file.intensity.astype(np.float64)
            intensity_normalized = (intensity - intensity.min()) / (intensity.max() - intensity.min())
            # Use intensity as grayscale color (R=G=B=intensity)
            colors = np.column_stack((intensity_normalized, intensity_normalized, intensity_normalized))
            pcd.colors = o3d.utility.Vector3dVector(colors)
            print(f"Added intensity information as colors")

        # Save as PCD file
        print(f"Saving PCD file to {output_pcd_path}")
        success = o3d.io.write_point_cloud(output_pcd_path, pcd)

        if success:
            file_size = os.path.getsize(output_pcd_path)
            print(f"✓ Conversion successful!")
            print(f"Output file created: {output_pcd_path} ({file_size} bytes)")
            return True
        else:
            print("✗ Error: Failed to write PCD file")
            return False

    except Exception as e:
        print(f"✗ Error during conversion: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to convert the specific LAS file to PCD."""

    # Input LAS file path
    input_las = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.las"

    # Generate output PCD file path
    input_path = Path(input_las)
    output_pcd = input_path.parent / f"{input_path.stem}.pcd"

    print("LAS to PCD Conversion with Coordinate Reprojection")
    print("=" * 50)
    print(f"Input LAS file: {input_las}")
    print(f"Output PCD file: {output_pcd}")
    print()

    # Perform conversion (coordinates appear to already be in EPSG:32633)
    success = convert_las_to_pcd_with_reprojection(
        input_las_path=str(input_las),
        output_pcd_path=str(output_pcd),
        in_srs="EPSG:32633",
        out_srs="EPSG:32633"
    )

    if success:
        print("\n✓ Conversion completed successfully!")
        print(f"PCD file saved to: {output_pcd}")
    else:
        print("\n✗ Conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
