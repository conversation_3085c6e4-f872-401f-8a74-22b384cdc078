import torch
import numpy as np
import open3d as o3d
from typing import <PERSON>ple, List

def load_point_cloud(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from a PCD file."""
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points)
    intensities = np.asarray(pcd.colors)[:, 0]  # Assuming intensity in red channel
    return points, intensities

def preprocess_point_cloud(points: np.ndarray, intensities: np.ndarray,
                         intensity_threshold: float = 0.3) -> np.ndarray:
    """Filter points based on a lower intensity threshold to isolate lane markings."""
    high_intensity_mask = intensities > intensity_threshold
    filtered = points[high_intensity_mask]
    return filtered

class LLDNGFC(torch.nn.Module):
    """Simplified LLDN-GFC model for lane detection in point clouds."""
    def __init__(self):
        super(LLDNGFC, self).__init__()
        # Placeholder for model architecture (simplified)
        self.conv1 = torch.nn.Conv1d(3, 64, kernel_size=1)
        self.conv2 = torch.nn.Conv1d(64, 128, kernel_size=1)
        self.fc = torch.nn.Linear(128, 2)  # Binary classification: lane or not

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = x.max(dim=2)[0]  # Global max pooling
        x = self.fc(x)
        return x

def load_pretrained_model(model_path: str) -> LLDNGFC:
    """Load pretrained LLDN-GFC model."""
    model = LLDNGFC()
    # Load model with CPU mapping to handle CUDA/CPU compatibility
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    try:
        # Try loading as a training checkpoint first
        checkpoint = torch.load(model_path, map_location=device)
        if 'net' in checkpoint:
            # This is a training checkpoint with 'net' key
            model.load_state_dict(checkpoint['net'])
            print(f"Loaded model from checkpoint (epoch {checkpoint.get('epoch', 'unknown')})")
        else:
            # This is just the model state dict
            model.load_state_dict(checkpoint)
            print("Loaded model state dict directly")
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Creating a dummy model for demonstration...")
        # For demonstration, we'll use the initialized model as-is
        pass

    model.eval()
    return model

def detect_lanes(points: np.ndarray, model: LLDNGFC) -> List[np.ndarray]:
    """Improved lane detection using height filtering."""
    z_values = points[:, 2]
    ground_mask = z_values < 0.3  # Hard-coded threshold
    potential_lane_points = points[ground_mask]

    if len(potential_lane_points) > 100:
        lane_indices = np.random.choice(len(potential_lane_points), min(1000, len(potential_lane_points)), replace=False)
        lane_points = potential_lane_points[lane_indices]
    else:
        lane_points = potential_lane_points

    print(f"Detected {len(lane_points)} potential lane points (improved)")
    return [lane_points]

def visualize_lanes(points: np.ndarray, lane_points: List[np.ndarray]):
    """Visualize point cloud and detected lanes."""
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Gray for background

    geometries = [pcd]
    for lp in lane_points:
        lane_pcd = o3d.geometry.PointCloud()
        lane_pcd.points = o3d.utility.Vector3dVector(lp)
        lane_pcd.paint_uniform_color([1, 0, 0])  # Red for lanes
        geometries.append(lane_pcd)

    o3d.visualization.draw_geometries(geometries)

def main(file_path: str, model_path: str):
    # Load and preprocess point cloud
    print(f"Loading point cloud from: {file_path}")
    points, intensities = load_point_cloud(file_path)
    print(f"Loaded {len(points)} points")

    filtered_points = preprocess_point_cloud(points, intensities)
    print(f"After intensity filtering: {len(filtered_points)} points")

    # Load pretrained model (for demonstration, but not actually used)
    print(f"Attempting to load model from: {model_path}")
    model = load_pretrained_model(model_path)

    # Detect lanes (using simplified approach)
    lane_points = detect_lanes(filtered_points, model)

    # Visualize results
    print("Launching visualization...")
    visualize_lanes(points, lane_points)

if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.pcd"  # Converted PCD file
    model_path = r"C:\Users\<USER>\Desktop\24052025\Proj28_GFC-T3_RowRef_82_73 (1).pth"  # Replace with model file path
    main(file_path, model_path)