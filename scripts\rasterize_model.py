import torch
import numpy as np
import open3d as o3d
from typing import <PERSON>ple, List

def load_point_cloud(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from a PCD file."""
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points)
    intensities = np.asarray(pcd.colors)[:, 0]  # Assuming intensity in red channel
    return points, intensities

def preprocess_point_cloud(points: np.ndarray, intensities: np.ndarray,
                         intensity_threshold: float = 0.3) -> np.ndarray:
    """Filter points based on a lower intensity threshold to isolate lane markings."""
    high_intensity_mask = intensities > intensity_threshold
    filtered = points[high_intensity_mask]
    return filtered

class LLDNGFC(torch.nn.Module):
    """Simplified LLDN-GFC model for lane detection in point clouds."""
    def __init__(self):
        super(LLDNGFC, self).__init__()
        # Placeholder for model architecture (simplified)
        self.conv1 = torch.nn.Conv1d(3, 64, kernel_size=1)
        self.conv2 = torch.nn.Conv1d(64, 128, kernel_size=1)
        self.fc = torch.nn.Linear(128, 2)  # Binary classification: lane or not

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = x.max(dim=2)[0]  # Global max pooling
        x = self.fc(x)
        return x

def load_pretrained_model(model_path: str) -> LLDNGFC:
    """Load pretrained LLDN-GFC model."""
    model = LLDNGFC()
    # Load model with CPU mapping to handle CUDA/CPU compatibility
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    try:
        # Try loading as a training checkpoint first
        checkpoint = torch.load(model_path, map_location=device)
        if 'net' in checkpoint:
            # This is a training checkpoint with 'net' key
            model.load_state_dict(checkpoint['net'])
            print(f"Loaded model from checkpoint (epoch {checkpoint.get('epoch', 'unknown')})")
        else:
            # This is just the model state dict
            model.load_state_dict(checkpoint)
            print("Loaded model state dict directly")
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Creating a dummy model for demonstration...")
        # For demonstration, we'll use the initialized model as-is
        pass

    model.eval()
    return model

def detect_lanes(points: np.ndarray, model: LLDNGFC) -> List[np.ndarray]:
    """Improved lane detection using height filtering and line fitting."""
    from sklearn.cluster import DBSCAN
    from sklearn.linear_model import RANSACRegressor

    # Filter points near ground level
    z_values = points[:, 2]
    z_min = np.min(z_values)
    ground_mask = (z_values - z_min) < 0.5  # Points within 0.5m of lowest point
    ground_points = points[ground_mask]

    if len(ground_points) < 50:
        print("Not enough ground points for lane detection")
        return []

    print(f"Found {len(ground_points)} ground-level points")

    # Cluster points to find potential lane segments
    clustering = DBSCAN(eps=2.0, min_samples=20).fit(ground_points[:, :2])  # Only X,Y for clustering
    labels = clustering.labels_
    unique_labels = set(labels)
    unique_labels.discard(-1)  # Remove noise points

    lane_lines = []

    for label in unique_labels:
        cluster_mask = labels == label
        cluster_points = ground_points[cluster_mask]

        if len(cluster_points) < 30:
            continue

        # Sort points by X coordinate for line fitting
        sorted_indices = np.argsort(cluster_points[:, 0])
        sorted_points = cluster_points[sorted_indices]

        # Fit line using RANSAC for robustness
        X = sorted_points[:, 0].reshape(-1, 1)
        y = sorted_points[:, 1]

        try:
            ransac = RANSACRegressor(random_state=42, residual_threshold=1.0)
            ransac.fit(X, y)

            # Generate line points
            x_min, x_max = X.min(), X.max()
            x_line = np.linspace(x_min, x_max, 50)
            y_line = ransac.predict(x_line.reshape(-1, 1))
            z_line = np.full_like(x_line, np.mean(sorted_points[:, 2]))

            line_points = np.column_stack((x_line, y_line, z_line))
            lane_lines.append(line_points)

        except Exception as e:
            print(f"Failed to fit line for cluster {label}: {e}")
            continue

    print(f"Detected {len(lane_lines)} lane lines")
    return lane_lines

def visualize_lanes(points: np.ndarray, lane_lines: List[np.ndarray]):
    """Visualize point cloud and detected lane lines."""
    # Sample the original point cloud for better performance
    if len(points) > 50000:
        indices = np.random.choice(len(points), 50000, replace=False)
        sampled_points = points[indices]
    else:
        sampled_points = points

    # Create main point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(sampled_points)
    pcd.paint_uniform_color([0.7, 0.7, 0.7])  # Light gray for background

    geometries = [pcd]

    # Create line geometries for each detected lane
    colors = [[1, 0, 0], [0, 1, 0], [0, 0, 1], [1, 1, 0], [1, 0, 1], [0, 1, 1]]  # Different colors for different lanes

    for i, line_points in enumerate(lane_lines):
        if len(line_points) < 2:
            continue

        # Create line set geometry
        line_set = o3d.geometry.LineSet()
        line_set.points = o3d.utility.Vector3dVector(line_points)

        # Create line connections (connect consecutive points)
        lines = [[j, j + 1] for j in range(len(line_points) - 1)]
        line_set.lines = o3d.utility.Vector2iVector(lines)

        # Set color for this lane
        color = colors[i % len(colors)]
        line_colors = [color for _ in range(len(lines))]
        line_set.colors = o3d.utility.Vector3dVector(line_colors)

        geometries.append(line_set)

        # Also add points for better visibility
        lane_pcd = o3d.geometry.PointCloud()
        lane_pcd.points = o3d.utility.Vector3dVector(line_points)
        lane_pcd.paint_uniform_color(color)
        geometries.append(lane_pcd)

        print(f"Lane {i+1}: {len(line_points)} points, color: {color}")

    print(f"Visualizing {len(lane_lines)} lane lines with {len(sampled_points)} background points")
    o3d.visualization.draw_geometries(geometries)

def main(file_path: str, model_path: str):
    # Load and preprocess point cloud
    print(f"Loading point cloud from: {file_path}")
    points, intensities = load_point_cloud(file_path)
    print(f"Loaded {len(points)} points")

    filtered_points = preprocess_point_cloud(points, intensities)
    print(f"After intensity filtering: {len(filtered_points)} points")

    # Load pretrained model (for demonstration, but not actually used)
    print(f"Attempting to load model from: {model_path}")
    model = load_pretrained_model(model_path)

    # Detect lanes (using simplified approach)
    lane_points = detect_lanes(filtered_points, model)

    # Visualize results
    print("Launching visualization...")
    visualize_lanes(points, lane_points)

if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.pcd"  # Converted PCD file
    model_path = r"C:\Users\<USER>\Desktop\24052025\Proj28_GFC-T3_RowRef_82_73 (1).pth"  # Replace with model file path
    main(file_path, model_path)