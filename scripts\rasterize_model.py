import torch
import numpy as np
import open3d as o3d
from typing import <PERSON>ple, List

def load_point_cloud(file_path: str) -> Tuple[np.ndarray, np.ndarray]:
    """Load point cloud from a PCD file."""
    pcd = o3d.io.read_point_cloud(file_path)
    points = np.asarray(pcd.points)
    intensities = np.asarray(pcd.colors)[:, 0]  # Assuming intensity in red channel
    return points, intensities

def preprocess_point_cloud(points: np.ndarray, intensities: np.ndarray,
                         intensity_threshold: float = 0.8) -> np.ndarray:
    """Filter points based on intensity to isolate potential lane markings."""
    high_intensity_mask = intensities > intensity_threshold
    return points[high_intensity_mask]

class LLDNGFC(torch.nn.Module):
    """Simplified LLDN-GFC model for lane detection in point clouds."""
    def __init__(self):
        super(LLDNGFC, self).__init__()
        # Placeholder for model architecture (simplified)
        self.conv1 = torch.nn.Conv1d(3, 64, kernel_size=1)
        self.conv2 = torch.nn.Conv1d(64, 128, kernel_size=1)
        self.fc = torch.nn.Linear(128, 2)  # Binary classification: lane or not

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = x.max(dim=2)[0]  # Global max pooling
        x = self.fc(x)
        return x

def load_pretrained_model(model_path: str) -> LLDNGFC:
    """Load pretrained LLDN-GFC model."""
    model = LLDNGFC()
    model.load_state_dict(torch.load(model_path))
    model.eval()
    return model

def detect_lanes(points: np.ndarray, model: LLDNGFC) -> List[np.ndarray]:
    """Detect lanes using the pretrained model."""
    points_tensor = torch.from_numpy(points).float().permute(1, 0).unsqueeze(0)
    with torch.no_grad():
        predictions = model(points_tensor)
    lane_mask = predictions.argmax(dim=1).numpy()
    lane_points = points[lane_mask == 1]
    return [lane_points]  # Simplified: assumes one cluster

def visualize_lanes(points: np.ndarray, lane_points: List[np.ndarray]):
    """Visualize point cloud and detected lanes."""
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    pcd.paint_uniform_color([0.5, 0.5, 0.5])  # Gray for background

    geometries = [pcd]
    for lp in lane_points:
        lane_pcd = o3d.geometry.PointCloud()
        lane_pcd.points = o3d.utility.Vector3dVector(lp)
        lane_pcd.paint_uniform_color([1, 0, 0])  # Red for lanes
        geometries.append(lane_pcd)

    o3d.visualization.draw_geometries(geometries)

def main(file_path: str, model_path: str):
    # Load and preprocess point cloud
    points, intensities = load_point_cloud(file_path)
    filtered_points = preprocess_point_cloud(points, intensities)

    # Load pretrained model
    model = load_pretrained_model(model_path)

    # Detect lanes
    lane_points = detect_lanes(filtered_points, model)

    # Visualize results
    visualize_lanes(points, lane_points)

if __name__ == "__main__":
    file_path = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.pcd"  # Converted PCD file
    model_path = r"C:\Users\<USER>\Desktop\24052025\Proj28_GFC-T3_RowRef_82_73 (1).pth"  # Replace with model file path
    main(file_path, model_path)