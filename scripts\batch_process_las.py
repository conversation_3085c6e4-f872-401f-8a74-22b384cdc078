#!/usr/bin/env python3
"""
Batch processing script to run continuous_line.py on all LAS files in the only_las_files folder.
"""

import os
import sys
import subprocess
from pathlib import Path
import time
from datetime import datetime

def find_las_files(directory):
    """Find all .las files in the given directory."""
    las_files = []
    for file in Path(directory).glob("*.las"):
        las_files.append(file)
    return sorted(las_files)

def create_output_path(las_file, output_dir):
    """Create output GeoJSON path based on input LAS file name."""
    las_name = las_file.stem  # filename without extension
    output_file = output_dir / f"{las_name}_lanes.geojson"
    return output_file

def run_continuous_line(las_file, output_file, script_path):
    """Run the continuous_line.py script for a single LAS file."""
    cmd = [
        sys.executable,  # Use the same Python interpreter
        str(script_path),
        "--input", str(las_file),
        "--output", str(output_file)
    ]

    print(f"\n{'='*60}")
    print(f"Processing: {las_file.name}")
    print(f"Output: {output_file.name}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        # Run the script and capture output
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=1200  # 20 minute timeout per file
        )

        end_time = time.time()
        duration = end_time - start_time

        if result.returncode == 0:
            print(f"✅ SUCCESS - Processed in {duration:.1f}s")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return True, None
        else:
            print(f"❌ FAILED - Return code: {result.returncode}")
            error_msg = result.stderr if result.stderr else "Unknown error"
            print(f"Error: {error_msg}")
            if result.stdout:
                print("Output:")
                print(result.stdout)
            return False, error_msg

    except subprocess.TimeoutExpired:
        print(f"❌ TIMEOUT - Processing took longer than 5 minutes")
        return False, "Timeout after 5 minutes"
    except Exception as e:
        print(f"❌ EXCEPTION - {str(e)}")
        return False, str(e)

def main():
    # Set up paths
    workspace_root = Path.cwd()
    las_directory = workspace_root / "only_las_files"
    output_directory = workspace_root / "output_geojson"
    script_path = workspace_root / "scripts" / "continous_line.py"

    # Create output directory if it doesn't exist
    output_directory.mkdir(exist_ok=True)

    # Check if script exists
    if not script_path.exists():
        print(f"❌ Error: Script not found at {script_path}")
        return

    # Check if LAS directory exists
    if not las_directory.exists():
        print(f"❌ Error: LAS directory not found at {las_directory}")
        return

    # Find all LAS files
    las_files = find_las_files(las_directory)

    if not las_files:
        print(f"❌ No LAS files found in {las_directory}")
        return

    print(f"Found {len(las_files)} LAS files to process")
    print(f"Input directory: {las_directory}")
    print(f"Output directory: {output_directory}")
    print(f"Script: {script_path}")

    # Process each file
    successful = 0
    failed = 0
    errors = []

    start_time = datetime.now()

    for i, las_file in enumerate(las_files, 1):
        print(f"\n[{i}/{len(las_files)}] Processing {las_file.name}")

        output_file = create_output_path(las_file, output_directory)

        # Skip if output already exists (optional - comment out to overwrite)
        if output_file.exists():
            print(f"⚠️  Output file already exists: {output_file.name}")
            choice = input("Overwrite? (y/n/a for all): ").lower()
            if choice == 'n':
                print("Skipping...")
                continue
            elif choice == 'a':
                print("Overwriting all existing files...")
                # Continue processing without asking again
            # If 'y' or 'a', continue to process

        success, error = run_continuous_line(las_file, output_file, script_path)

        if success:
            successful += 1
        else:
            failed += 1
            errors.append((las_file.name, error))

    # Summary
    end_time = datetime.now()
    total_duration = end_time - start_time

    print(f"\n{'='*60}")
    print("BATCH PROCESSING COMPLETE")
    print(f"{'='*60}")
    print(f"Total files: {len(las_files)}")
    print(f"Successful: {successful}")
    print(f"Failed: {failed}")
    print(f"Total time: {total_duration}")
    print(f"Output directory: {output_directory}")

    if errors:
        print(f"\n❌ Failed files:")
        for filename, error in errors:
            print(f"  - {filename}: {error}")

    if successful > 0:
        print(f"\n✅ Successfully processed {successful} files!")
        print(f"GeoJSON files saved to: {output_directory}")

if __name__ == "__main__":
    main()
