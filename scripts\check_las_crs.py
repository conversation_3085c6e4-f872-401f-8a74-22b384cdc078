#!/usr/bin/env python3
"""
Check the coordinate reference system (CRS) of a LAS file.
"""

import laspy
import sys

def check_las_crs(las_file_path: str):
    """Check the CRS information in a LAS file."""

    try:
        # Read LAS file
        las_file = laspy.read(las_file_path)

        print(f"LAS File: {las_file_path}")
        print("=" * 60)

        # Basic file info
        print(f"Number of points: {len(las_file.points)}")
        print(f"LAS version: {las_file.header.version}")
        print(f"Point format: {las_file.header.point_format}")

        # Coordinate bounds
        print(f"\nCoordinate bounds:")
        print(f"  X: {las_file.header.x_min:.6f} to {las_file.header.x_max:.6f}")
        print(f"  Y: {las_file.header.y_min:.6f} to {las_file.header.y_max:.6f}")
        print(f"  Z: {las_file.header.z_min:.6f} to {las_file.header.z_max:.6f}")

        # Scale and offset
        print(f"\nScale factors:")
        print(f"  X scale: {las_file.header.x_scale}")
        print(f"  Y scale: {las_file.header.y_scale}")
        print(f"  Z scale: {las_file.header.z_scale}")

        print(f"\nOffset values:")
        print(f"  X offset: {las_file.header.x_offset}")
        print(f"  Y offset: {las_file.header.y_offset}")
        print(f"  Z offset: {las_file.header.z_offset}")

        # Check for CRS information
        print(f"\nCRS Information:")
        if hasattr(las_file.header, 'crs'):
            print(f"  CRS: {las_file.header.crs}")
        else:
            print("  No CRS information found in header")

        # Check VLRs (Variable Length Records) for CRS info
        if hasattr(las_file.header, 'vlrs'):
            print(f"\nVariable Length Records (VLRs): {len(las_file.header.vlrs)}")
            for i, vlr in enumerate(las_file.header.vlrs):
                print(f"  VLR {i}: {vlr.user_id} - {vlr.record_id}")
                if hasattr(vlr, 'description'):
                    print(f"    Description: {vlr.description}")

        # Sample some actual coordinate values
        print(f"\nSample coordinates (first 5 points):")
        for i in range(min(5, len(las_file.x))):
            print(f"  Point {i}: X={las_file.x[i]:.6f}, Y={las_file.y[i]:.6f}, Z={las_file.z[i]:.6f}")

        # Analyze coordinate ranges to guess CRS
        x_range = las_file.header.x_max - las_file.header.x_min
        y_range = las_file.header.y_max - las_file.header.y_min

        print(f"\nCoordinate Analysis:")
        print(f"  X range: {x_range:.6f}")
        print(f"  Y range: {y_range:.6f}")

        # Guess coordinate system based on values
        if (las_file.header.x_min > 100000 and las_file.header.x_max < 1000000 and
            las_file.header.y_min > 1000000 and las_file.header.y_max < 10000000):
            print("  Likely coordinate system: UTM or similar projected system")
            print("  Coordinates appear to be in meters")
        elif (las_file.header.x_min > -180 and las_file.header.x_max < 180 and
              las_file.header.y_min > -90 and las_file.header.y_max < 90):
            print("  Likely coordinate system: Geographic (lat/lon)")
            print("  Coordinates appear to be in degrees")
        else:
            print("  Coordinate system: Unknown or custom projection")

    except Exception as e:
        print(f"Error reading LAS file: {e}")
        import traceback
        traceback.print_exc()

def main():
    las_file_path = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.las"
    check_las_crs(las_file_path)

if __name__ == "__main__":
    main()
