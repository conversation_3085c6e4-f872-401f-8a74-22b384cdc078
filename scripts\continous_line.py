
# ───────────────────────────── Imports ──────────────────────────────
import json
import math
import os
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import List, Tuple, Dict, Optional

import laspy
import numpy as np
import open3d as o3d  # fast DBSCAN & KD‑Tree
from pyproj import Transformer
from scipy.spatial import cKDTree

# Optional / improve performance & features
try:
    from shapely.geometry import LineString, Point  # curvature + spatial ops
    from shapely.ops import unary_union
except ImportError:
    LineString = None  # graceful degradation

try:
    import skimage.measure as skmeasure  # symbol detection (stub)
    import skimage.morphology as skmorph
except ImportError:
    skmeasure = None

# ─────────────────────────── Configuration ─────────────────────────
LAS_PATH: Path = Path(r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974214032225_1422974242419323.las")
OUTPUT_GEOJSON: Path = Path(r"C:\Users\<USER>\Desktop\24052025\output_sample.geojson")

# Semantic thresholds (tune per sensor)
INTENSITY_PERCENTILE = 95        # top x% are paint
PHYSICAL_Z_MIN = 0.10            # m above road plane → physical delimiter lower bound
PHYSICAL_Z_MAX = 0.30            # m above road plane → upper bound

DBSCAN_EPS = 0.10                # m; cluster radius
DBSCAN_MIN_POINTS = 10           # pts per cluster

POLY_DEGREE = 1                  # lane line polyfit degree
SAMPLES_PER_LINE = 100           # sampled vertices per polyline

CURVATURE_RTHRESH = 250.0        # m; <→ road‑edge, >→ lane‑boundary
GAP_LEN_THR = 1.0                # m; missing segment ⇒ dashed candidate
DASHED_GAP_COUNT = 3             # ≥N gaps ⇒ dashed

# Performance optimization for large files
MAX_POINTS_FOR_CLUSTERING = 200000  # downsample if more points (reduced for faster processing)
DOWNSAMPLE_FACTOR = 0.05         # keep 5% of points if downsampling (more aggressive)

# Fallback colour model
WHITE_MEDIAN_MIN = 1000          # intensity units – tune!
WHITE_IQR_MAX = 500              # "

# CRS transformers (EPSG:25832 → WGS84 lon/lat)
TO_WGS84 = Transformer.from_crs("EPSG:25832", "EPSG:4326", always_xy=True)

# ──────────────────────────── Data models ───────────────────────────
@dataclass
class BoundaryAttrs:
    role: str                     # "road_boundary" | "lane_boundary"
    lane_index: int               # 0 = rightmost in RHS country
    colour: str                   # "white" | "yellow" | "unknown"
    style: str                    # "solid" | "dashed" | "unknown"
    material: str                 # "painted" | "physical"
    direction: str                # "forward" | "backward" | "unknown"

# ────────────────────────── Helper functions ────────────────────────

def load_las(path: Path):
    las = laspy.read(str(path))
    pts = np.vstack((las.x, las.y, las.z)).T.astype(np.float32)
    intens = las.intensity.astype(np.float32)
    return pts, intens


def classify_points(pts: np.ndarray, intens: np.ndarray):
    """Return two boolean masks: paint_pts & physical_pts."""
    paint_thr = np.percentile(intens, INTENSITY_PERCENTILE)
    paint_mask = intens >= paint_thr

    z = pts[:, 2]
    ground_z = np.percentile(z, 2)  # crude road plane
    z_rel = z - ground_z
    phys_mask = (z_rel > PHYSICAL_Z_MIN) & (z_rel < PHYSICAL_Z_MAX)

    return paint_mask, phys_mask


def downsample_points(points: np.ndarray, max_points: int) -> np.ndarray:
    """Downsample points if there are too many for efficient clustering."""
    if len(points) <= max_points:
        return points

    # Random downsampling
    np.random.seed(42)  # For reproducible results
    indices = np.random.choice(len(points), max_points, replace=False)
    print(f"  Downsampling from {len(points):,} to {max_points:,} points")
    return points[indices]


def cluster_points(points: np.ndarray) -> List[np.ndarray]:
    """DBSCAN clustering with Open3D; returns list of Nx3 arrays."""
    if len(points) == 0:
        return []

    # Downsample if too many points
    if len(points) > MAX_POINTS_FOR_CLUSTERING:
        points = downsample_points(points, MAX_POINTS_FOR_CLUSTERING)

    print(f"  Clustering {len(points):,} points...")
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    labels = np.array(
        pcd.cluster_dbscan(eps=DBSCAN_EPS, min_points=DBSCAN_MIN_POINTS, print_progress=True)
    )
    clusters = [points[labels == lab] for lab in set(labels) if lab >= 0]
    print(f"  Found {len(clusters)} clusters")
    return clusters


def fit_polylines(clusters: List[np.ndarray]) -> List[np.ndarray]:
    """Poly‑fit each cluster and resample; returns list of sampled Nx3 lines."""
    polylines = []
    for pts in clusters:
        xy = pts[:, :2]
        zs = pts[:, 2]
        order = np.argsort(xy[:, 0])  # sort along X
        x_sorted, y_sorted = xy[order, 0], xy[order, 1]
        coeffs = np.polyfit(x_sorted, y_sorted, POLY_DEGREE)
        xs = np.linspace(x_sorted.min(), x_sorted.max(), SAMPLES_PER_LINE)
        ys = np.polyval(coeffs, xs)
        # nearest Z from original pts
        tree = cKDTree(xy)
        _, idx = tree.query(np.column_stack((xs, ys)))
        zs_smpl = zs[idx]
        polylines.append(np.column_stack((xs, ys, zs_smpl)))
    return polylines


def curvature_radius(line: np.ndarray) -> float:
    """Approximate radius of curvature (mean over samples). Returns inf for straight."""
    if LineString is None:
        return math.inf
    line2d = LineString(line[:, :2])
    if line2d.length < 1e-6:
        return math.inf
    # sample equally spaced points along length
    num = min(20, len(line))
    dists = np.linspace(0, line2d.length, num)
    sampled = [np.array(line2d.interpolate(d).coords[0]) for d in dists]
    sampled = np.asarray(sampled)
    # compute osculating circles through triplets
    radii = []
    for a, b, c in zip(sampled[:-2], sampled[1:-1], sampled[2:]):
        R = _circle_radius(a, b, c)
        if not math.isinf(R):
            radii.append(R)
    return np.median(radii) if radii else math.inf


def _circle_radius(a: np.ndarray, b: np.ndarray, c: np.ndarray) -> float:
    """Radius of circle through 3 points; return inf if collinear."""
    ax, ay = a
    bx, by = b
    cx, cy = c
    d = 2 * (ax * (by - cy) + bx * (cy - ay) + cx * (ay - by))
    if abs(d) < 1e-6:
        return math.inf
    ux = ((ax**2 + ay**2) * (by - cy) + (bx**2 + by**2) * (cy - ay) + (cx**2 + cy**2) * (ay - by)) / d
    uy = ((ax**2 + ay**2) * (cx - bx) + (bx**2 + by**2) * (ax - cx) + (cx**2 + cy**2) * (bx - ax)) / d
    r = math.hypot(ax - ux, ay - uy)
    return r


def detect_style(line: np.ndarray) -> str:
    """Solid vs dashed based on missing sample gaps > GAP_LEN_THR."""
    seglens = np.linalg.norm(np.diff(line[:, :2], axis=0), axis=1)
    gaps = seglens > GAP_LEN_THR
    if np.count_nonzero(gaps) >= DASHED_GAP_COUNT:
        return "dashed"
    return "solid"


def estimate_colour(intens: np.ndarray) -> str:
    p50 = np.median(intens)
    iqr = np.percentile(intens, 75) - np.percentile(intens, 25)
    if p50 > WHITE_MEDIAN_MIN and iqr < WHITE_IQR_MAX:
        return "white"
    return "yellow"


def assign_lane_indices(boundaries: List[np.ndarray]) -> List[int]:
    """Slice across X principal axis, sort by Y, assign indices."""
    if not boundaries:
        return []
    # crude road heading from PCA of all pts
    all_pts = np.vstack([ln[:, :2] for ln in boundaries])
    cov = np.cov(all_pts.T)
    eigvals, eigvecs = np.linalg.eigh(cov)
    major_axis = eigvecs[:, np.argmax(eigvals)]  # unit vector
    # Rotate to align major axis with +X
    theta = math.atan2(major_axis[1], major_axis[0])
    R = np.array([[math.cos(-theta), -math.sin(-theta)], [math.sin(-theta), math.cos(-theta)]])
    centroids = [ln.mean(axis=0)[:2] @ R.T for ln in boundaries]
    # sort by Y (cross‑road direction); rightmost (min Y) => index 0 under RHS traffic
    sorted_idx = np.argsort([c[1] for c in centroids])
    lane_idx = [None] * len(boundaries)
    for new_idx, old in enumerate(sorted_idx):
        lane_idx[old] = new_idx
    return lane_idx


def determine_direction(lane_idx: List[int], num_lanes: int) -> List[str]:
    """Assign forward/backward assuming RHS traffic and no arrow info."""
    directions = []
    half = num_lanes // 2
    for idx in lane_idx:
        if idx is None:
            directions.append("unknown")
        elif idx < half:
            directions.append("forward")
        else:
            directions.append("backward")
    return directions

# ────────────────────────── GeoJSON helpers ────────────────────────

def line_to_geojson(line: np.ndarray) -> List[List[float]]:
    return [list(TO_WGS84.transform(float(x), float(y))) + [float(z)] for x, y, z in line]

def merge_polylines_kdtree(polylines: List[np.ndarray], max_gap: float = 0.5) -> List[np.ndarray]:
    """Merge polylines whose ends are within max_gap distance using KD-Tree for efficiency."""
    if not polylines:
        return []

    # Create endpoints database
    endpoints = []  # [(x, y, line_idx, is_start)]
    for i, line in enumerate(polylines):
        endpoints.append((line[0][0], line[0][1], i, True))   # start point
        endpoints.append((line[-1][0], line[-1][1], i, False)) # end point

    # Build KD-tree of all endpoints
    points = np.array([(x, y) for x, y, _, _ in endpoints])
    tree = cKDTree(points)

    # Track merged lines
    used = [False] * len(polylines)
    merged = []

    for i, line1 in enumerate(polylines):
        if used[i]:
            continue

        # Start new merged line
        current = list(line1)
        used[i] = True

        while True:
            # Find nearest endpoints to current line's end
            current_end = np.array([current[-1][0], current[-1][1]])
            distances, indices = tree.query(current_end, k=8, distance_upper_bound=max_gap)

            found_connection = False
            for dist, idx in zip(distances, indices):
                if dist > max_gap or idx >= len(endpoints):
                    continue

                _, _, line_idx, is_start = endpoints[idx]
                if used[line_idx] or line_idx == i:
                    continue

                # Connect the lines
                other_line = polylines[line_idx]
                if is_start:
                    current.extend(other_line)
                else:
                    current.extend(other_line[::-1])  # reverse

                used[line_idx] = True
                found_connection = True
                break

            if not found_connection:
                break

        merged.append(np.array(current))

    return merged

# ──────────────────────────── Main routine ─────────────────────────

def main(las_path: Path = LAS_PATH, output_geojson: Path = OUTPUT_GEOJSON):
    print(f"Loading LAS -> {las_path}")
    pts, intens = load_las(las_path)
    print(f"Loaded {len(pts):,} pts")

    # Early downsampling for very large files
    if len(pts) > 1000000:  # 1M points
        downsample_ratio = min(0.3, 500000 / len(pts))  # Keep max 30% or 500k points
        n_keep = int(len(pts) * downsample_ratio)
        np.random.seed(42)
        indices = np.random.choice(len(pts), n_keep, replace=False)
        pts = pts[indices]
        intens = intens[indices]
        print(f"Early downsampling: kept {len(pts):,} points ({downsample_ratio:.1%})")

    # 1. semantics masks
    paint_mask, phys_mask = classify_points(pts, intens)
    paint_pts = pts[paint_mask]
    phys_pts = pts[phys_mask]
    print(f"Paint pts: {len(paint_pts):,} | Physical pts: {len(phys_pts):,}")

    # 2. cluster & fit polylines for each material
    polylines = []
    materials = []
    for subpts, mat in ((paint_pts, "painted"), (phys_pts, "physical")):
        clusters = cluster_points(subpts)
        print(f"  {mat}: {len(clusters)} clusters")
        fitted = fit_polylines(clusters)

        # Merge fitted polylines using KD-Tree optimization
        fitted = merge_polylines_kdtree(fitted, max_gap=0.5)
        print(f"  {mat}: merged into {len(fitted)} continuous lines")

        polylines.extend(fitted)
        materials.extend([mat] * len(fitted))

    if not polylines:
        print("No polylines extracted – aborting.")
        return

    # 3. role via curvature radius
    roles = ["road_boundary" if curvature_radius(ln) < CURVATURE_RTHRESH else "lane_boundary" for ln in polylines]

    # 4. style & colour & lane_index
    styles = [detect_style(ln) for ln in polylines]
    colours = [estimate_colour(intens[paint_mask][i]) if materials[i] == "painted" else "unknown" for i in range(len(polylines))]

    lane_indices = assign_lane_indices([ln for ln, r in zip(polylines, roles) if r == "lane_boundary"])
    # create mapping back to all lines (road edges get -1)
    lane_idx_full = []
    j = 0
    for r in roles:
        if r == "lane_boundary":
            lane_idx_full.append(lane_indices[j])
            j += 1
        else:
            lane_idx_full.append(-1)
    num_lanes = max([idx for idx in lane_idx_full if idx >= 0], default=-1) + 1

    # 5. direction heuristics
    directions = determine_direction(lane_idx_full, num_lanes) if num_lanes > 0 else ["unknown"] * len(polylines)

    # 6. Build GeoJSON
    features = []
    for i, line in enumerate(polylines):
        props = BoundaryAttrs(
            role=roles[i],
            lane_index=lane_idx_full[i],
            colour=colours[i],
            style=styles[i],
            material=materials[i],
            direction=directions[i],
        )
        features.append({
            "type": "Feature",
            "properties": asdict(props),
            "geometry": {
                "type": "LineString",
                "coordinates": line_to_geojson(line)
            }
        })

    geo = {"type": "FeatureCollection", "features": features}
    output_geojson.parent.mkdir(parents=True, exist_ok=True)
    with open(output_geojson, 'w', encoding='utf-8') as f:
        json.dump(geo, f, indent=2)
    print(f"SUCCESS: Saved {len(features)} features -> {output_geojson}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Extract lane markings from LAS files")
    parser.add_argument("--input", "-i", type=Path, help="Input LAS file path")
    parser.add_argument("--output", "-o", type=Path, help="Output GeoJSON file path")

    args = parser.parse_args()

    # Use command line arguments if provided, otherwise use defaults
    las_path = args.input if args.input else LAS_PATH
    output_path = args.output if args.output else OUTPUT_GEOJSON

    main(las_path, output_path)

# import argparse
# import json
# import logging
# from pathlib import Path

# import laspy
# import numpy as np
# import open3d as o3d
# from scipy.interpolate import splprep, splev
# from scipy.spatial import cKDTree
# from sklearn.cluster import DBSCAN
# from sklearn.mixture import GaussianMixture
# import hdbscan
# from skimage.filters import threshold_otsu
# from skimage.morphology import opening, square
# from sklearn.decomposition import PCA
# from joblib import Parallel, delayed
# import networkx as nx


# def setup_logger(level=logging.INFO):
#     logging.basicConfig(
#         format="%(asctime)s [%(levelname)s] %(message)s",
#         level=level,
#     )
#     return logging.getLogger("LaneExtractor")


# class LaneExtractor:
#     def __init__(self, args):
#         self.logger = setup_logger()
#         self.args = args
#         # Pre-compute plane normal placeholder
#         self.plane_normal = None
#         self.plane_offset = None

#     def load_cloud(self):
#         las = laspy.read(str(self.args.las_path))
#         pts = np.vstack((las.x, las.y, las.z)).T.astype(np.float32)
#         intens = las.intensity.astype(np.float32)
#         self.logger.info(f"Loaded {len(pts)} points")
#         # Open3D point cloud
#         pcd = o3d.geometry.PointCloud()
#         pcd.points = o3d.utility.Vector3dVector(pts)
#         pcd.colors = o3d.utility.Vector3dVector(
#             np.tile((intens / intens.max())[:, None], (1, 3))
#         )
#         # Downsample
#         self.pcd = pcd.voxel_down_sample(voxel_size=self.args.voxel_size)
#         self.logger.info(f"Downsampled to {len(self.pcd.points)} points")
#         # Estimate normals
#         self.pcd.estimate_normals(
#             search_param=o3d.geometry.KDTreeSearchParamHybrid(
#                 radius=self.args.normal_radius,
#                 max_nn=self.args.normal_max_nn,
#             )
#         )

#     def estimate_ground(self):
#         plane_model, inliers = self.pcd.segment_plane(
#             distance_threshold=self.args.plane_dist_thr,
#             ransac_n=3,
#             num_iterations=self.args.plane_iters,
#         )
#         [a, b, c, d] = plane_model
#         self.plane_normal = np.array([a, b, c])
#         self.plane_offset = d
#         self.logger.info("Estimated ground plane: %.3f x + %.3f y + %.3f z + %.3f = 0",
#                          a, b, c, d)

#     def classify_points(self):
#         pts = np.asarray(self.pcd.points)
#         intens = np.asarray(self.pcd.colors)[:, 0] * self.args.intensity_scale
#         # Paint: Otsu threshold
#         thr = threshold_otsu(intens)
#         paint_mask = intens >= thr
#         self.logger.info("Otsu intensity threshold = %.2f", thr)
#         # Physical: height above plane + normal orientation
#         heights = (pts @ self.plane_normal + self.plane_offset) / np.linalg.norm(self.plane_normal)
#         normals = np.asarray(self.pcd.normals)
#         orient = np.abs(normals @ self.plane_normal / np.linalg.norm(self.plane_normal))
#         phys_mask = (
#             (heights > self.args.phys_z_min) &
#             (heights < self.args.phys_z_max) &
#             (orient > self.args.phys_norm_cos)
#         )
#         self.paint_pts = pts[paint_mask]
#         self.paint_int = intens[paint_mask]
#         self.phys_pts = pts[phys_mask]
#         self.phys_int = intens[phys_mask]
#         self.logger.info("Paint pts: %d | Physical pts: %d",
#                          len(self.paint_pts), len(self.phys_pts))

#     def cluster_markings(self):
#         clusters = []
#         materials = []
#         # Paint: HDBSCAN
#         if len(self.paint_pts) > 0:
#             clusterer = hdbscan.HDBSCAN(
#                 min_cluster_size=self.args.paint_min_cluster,
#                 metric='euclidean'
#             )
#             labels = clusterer.fit_predict(self.paint_pts[:, :2])
#             for lab in set(labels):
#                 if lab < 0:
#                     continue
#                 mask = labels == lab
#                 clusters.append(self.paint_pts[mask])
#                 materials.append('painted')
#             self.paint_labels = labels
#             self.logger.info("Paint clusters: %d", len(set(labels)) - (1 if -1 in labels else 0))
#         # Physical: DBSCAN
#         if len(self.phys_pts) > 0:
#             db = DBSCAN(
#                 eps=self.args.phys_dbscan_eps,
#                 min_samples=self.args.phys_min_samples
#             )
#             labels = db.fit_predict(self.phys_pts[:, :2])
#             for lab in set(labels):
#                 if lab < 0:
#                     continue
#                 mask = labels == lab
#                 clusters.append(self.phys_pts[mask])
#                 materials.append('physical')
#             self.phys_labels = labels
#             self.logger.info("Physical clusters: %d", len(set(labels)) - (1 if -1 in labels else 0))
#         self.clusters = clusters
#         self.materials = materials

#     def fit_splines(self):
#         def fit_one(pts):
#             xy = pts[:, :2]
#             # sort points along principal axis
#             pca = PCA(n_components=1)
#             t = pca.fit_transform(xy)
#             order = np.argsort(t[:, 0])
#             x, y = xy[order, 0], xy[order, 1]
#             # spline fit
#             try:
#                 tck, u = splprep([x, y], s=self.args.spline_smooth)
#                 u_new = np.linspace(0, 1, self.args.spline_samples)
#                 xs, ys = splev(u_new, tck)
#             except Exception:
#                 # fallback linear
#                 xs = np.linspace(x.min(), x.max(), self.args.spline_samples)
#                 ys = np.interp(xs, x, y)
#             # sample z by nearest neighbor
#             tree = cKDTree(xy)
#             _, idx = tree.query(np.column_stack((xs, ys)))
#             zs = pts[:, 2][idx]
#             return np.column_stack((xs, ys, zs))

#         lines = Parallel(n_jobs=self.args.jobs)(
#             delayed(fit_one)(c) for c in self.clusters
#         )
#         self.lines = lines
#         self.logger.info("Fitted %d polylines", len(lines))

#     def compute_curvature(self, line):
#         # approximate via triplets
#         pts2d = line[:, :2]
#         radii = []
#         for i in range(len(pts2d) - 2):
#             a, b, c = pts2d[i], pts2d[i+1], pts2d[i+2]
#             # circle radius
#             d = 2 * (a[0]*(b[1]-c[1]) + b[0]*(c[1]-a[1]) + c[0]*(a[1]-b[1]))
#             if abs(d) < 1e-6:
#                 continue
#             ux = ((a[0]**2 + a[1]**2)*(b[1]-c[1]) +
#                   (b[0]**2 + b[1]**2)*(c[1]-a[1]) +
#                   (c[0]**2 + c[1]**2)*(a[1]-b[1])) / d
#             uy = ((a[0]**2 + a[1]**2)*(c[0]-b[0]) +
#                   (b[0]**2 + b[1]**2)*(a[0]-c[0]) +
#                   (c[0]**2 + c[1]**2)*(b[0]-a[0])) / d
#             r = np.hypot(a[0]-ux, a[1]-uy)
#             radii.append(r)
#         return np.median(radii) if radii else np.inf

#     def detect_style(self, line, intens_profile):
#         # binary profile by opening
#         binary = intens_profile > self.args.dash_int_thr
#         cleaned = opening(binary.astype(np.uint8), square(self.args.dash_morph_size))
#         segments = np.diff(cleaned.astype(int)) == 1
#         gaps = np.diff(cleaned.astype(int)) == -1
#         if segments.sum() > self.args.dash_min_segments:
#             return 'dashed'
#         return 'solid'

#     def estimate_color(self, intens):
#         gm = GaussianMixture(n_components=2)
#         gm.fit(intens.reshape(-1, 1))
#         means = gm.means_.flatten()
#         thr = means.mean()
#         return 'white' if intens.mean() > thr else 'yellow'

#     def assign_lanes(self):
#         # role by curvature
#         roles = []
#         for ln in self.lines:
#             r = 'road_boundary' if self.compute_curvature(ln) < self.args.curv_rthr else 'lane_boundary'
#             roles.append(r)
#         # collect lane-boundary indices
#         lb_idx = [i for i, r in enumerate(roles) if r=='lane_boundary']
#         centroids = []
#         for i in lb_idx:
#             cxy = self.lines[i][:, :2].mean(axis=0)
#             centroids.append(cxy)
#         centroids = np.array(centroids)
#         # build graph
#         G = nx.Graph()
#         G.add_nodes_from(range(len(lb_idx)))
#         for i in range(len(lb_idx)):
#             for j in range(i+1, len(lb_idx)):
#                 if np.linalg.norm(centroids[i]-centroids[j]) < self.args.lane_graph_eps:
#                     G.add_edge(i, j)
#         comps = list(nx.connected_components(G))
#         lane_idx_full = [-1]*len(self.lines)
#         for comp in comps:
#             comp_centroids = centroids[list(comp)]
#             order = np.argsort(comp_centroids[:, 1])  # sort by Y
#             for rank, idx in enumerate(order):
#                 orig = lb_idx[list(comp)[idx]]
#                 lane_idx_full[orig] = rank
#         # direction same as original heuristic
#         num_lanes = max([i for i in lane_idx_full if i>=0], default=-1) + 1
#         dirs = []
#         half = num_lanes // 2
#         for idx in lane_idx_full:
#             if idx < 0:
#                 dirs.append('unknown')
#             elif idx < half:
#                 dirs.append('forward')
#             else:
#                 dirs.append('backward')
#         self.roles = roles
#         self.lane_indices = lane_idx_full
#         self.directions = dirs

#     def to_geojson(self, out_path: Path):
#         from pyproj import Transformer
#         TO_WGS84 = Transformer.from_crs(self.args.crs_in, self.args.crs_out, always_xy=True)
#         features = []
#         for i, line in enumerate(self.lines):
#             attr = {
#                 'role': self.roles[i],
#                 'lane_index': self.lane_indices[i],
#                 'colour': self.estimate_color(
#                     (self.paint_int if self.materials[i]=='painted' else self.phys_int)
#                 ) if self.materials[i]=='painted' else 'unknown',
#                 'style': self.detect_style(
#                     line,
#                     np.asarray([self.paint_int[self.paint_labels==i].mean() if self.paint_labels is not None else 0])
#                 ),
#                 'material': self.materials[i],
#                 'direction': self.directions[i],
#             }
#             coords = [list(TO_WGS84.transform(float(x), float(y)))+[float(z)] for x,y,z in line]
#             features.append({'type':'Feature', 'properties':attr,
#                              'geometry':{'type':'LineString','coordinates':coords}})
#         geo = {'type':'FeatureCollection','features':features}
#         out_path.parent.mkdir(parents=True, exist_ok=True)
#         with open(out_path, 'w') as f:
#             json.dump(geo, f, indent=2)
#         self.logger.info("Saved %d features to %s", len(features), out_path)

#     def run(self):
#         self.load_cloud()
#         self.estimate_ground()
#         self.classify_points()
#         self.cluster_markings()
#         self.fit_splines()
#         self.assign_lanes()
#         self.to_geojson(self.args.output)


# def parse_args():
#     p = argparse.ArgumentParser(description="Lane Marking Extractor")
#     p.add_argument("--las_path", type=Path, required=True)
#     p.add_argument("--output", type=Path, required=True)
#     p.add_argument("--voxel_size", type=float, default=0.1)
#     p.add_argument("--normal_radius", type=float, default=0.5)
#     p.add_argument("--normal_max_nn", type=int, default=30)
#     p.add_argument("--plane_dist_thr", type=float, default=0.02)
#     p.add_argument("--plane_iters", type=int, default=1000)
#     p.add_argument("--intensity_scale", type=float, default=1.0)
#     p.add_argument("--phys_z_min", type=float, default=0.1)
#     p.add_argument("--phys_z_max", type=float, default=0.3)
#     p.add_argument("--phys_norm_cos", type=float, default=0.7)
#     p.add_argument("--paint_min_cluster", type=int, default=10)
#     p.add_argument("--phys_dbscan_eps", type=float, default=0.1)
#     p.add_argument("--phys_min_samples", type=int, default=10)
#     p.add_argument("--spline_smooth", type=float, default=1.0)
#     p.add_argument("--spline_samples", type=int, default=100)
#     p.add_argument("--curv_rthr", type=float, default=250.0)
#     p.add_argument("--dash_int_thr", type=float, default=0.5)
#     p.add_argument("--dash_morph_size", type=int, default=5)
#     p.add_argument("--dash_min_segments", type=int, default=3)
#     p.add_argument("--lane_graph_eps", type=float, default=1.0)
#     p.add_argument("--jobs", type=int, default=4)
#     p.add_argument("--crs_in", default="EPSG:25832")
#     p.add_argument("--crs_out", default="EPSG:4326")
#     return p.parse_args()


# if __name__ == '__main__':
#     args = parse_args()
#     extractor = LaneExtractor(args)
#     extractor.run()
