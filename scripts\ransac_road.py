# import os
# import json
# import numpy as np
# import open3d as o3d
# import laspy
# from pyproj import Transformer
# from sklearn.linear_model import RANSACRegressor, LinearRegression

# # Optional: for filled GeoJSON polygons
# try:
#     from shapely.geometry import Polygon as ShapelyPolygon
#     from shapely.ops import triangulate
#     SHAPELY_AVAILABLE = True
# except ImportError:
#     SHAPELY_AVAILABLE = False

# # ---------------- CONFIG ----------------
# # Single LAS file path
# LAS_PATH = r"C:\Users\<USER>\Desktop\HERE-Inputs\only_las_files\HT412_1738935654_3217135_1422974242419323_1422974299193520.las"
# # Folder containing GeoJSON features (each in a subfolder)
# FEATURE_ROOT = r"C:\Users\<USER>\Desktop\HERE-Inputs\features_split_new"

# # CRS transformation: GeoJSON EPSG:4326 → LAS EPSG:25832
# TRANSFORMER = Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

# # RANSAC settings
# PLANE_DIST_THRESH = 0.2
# PLANE_RANSAC_N = 3
# PLANE_ITER = 1000
# LINE_DIST_THRESH = 0.1
# MIN_LINE_POINTS = 200
# # ----------------------------------------

# def transform_coords(coords):
#     return np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in coords])


# def extract_3d_coords(geometry):
#     t = geometry.get("type", "")
#     coords = geometry.get("coordinates", [])
#     if t == "LineString":
#         return coords
#     if t == "MultiLineString":
#         return [pt for line in coords for pt in line]
#     if t == "Polygon":
#         return coords[0]
#     if t == "MultiPolygon":
#         pts = []
#         for poly in coords:
#             pts.extend(poly[0])
#         return pts
#     return []


# def load_las_as_pcd(las_path):
#     print(f"Loading LAS: {las_path}")
#     las = laspy.read(las_path)
#     pts = np.vstack((las.x, las.y, las.z)).T
#     pcd = o3d.geometry.PointCloud()
#     pcd.points = o3d.utility.Vector3dVector(pts)
#     print("Transforming LAS to target CRS...")
#     transformed = np.array([TRANSFORMER.transform(x, y, z) for x, y, z in pts])
#     pcd.points = o3d.utility.Vector3dVector(transformed)
#     return pcd


# def make_line_set(points, close_loop=False, color=(1,0,0)):
#     pts = o3d.utility.Vector3dVector(points)
#     lines = [[i,i+1] for i in range(len(points)-1)]
#     if close_loop and len(points)>2:
#         lines.append([len(points)-1,0])
#     ls = o3d.geometry.LineSet(points=pts, lines=o3d.utility.Vector2iVector(lines))
#     ls.colors = o3d.utility.Vector3dVector([color]*len(lines))
#     return ls


# def make_point_cloud(points, color=(1,0,0)):
#     pc = o3d.geometry.PointCloud()
#     pc.points = o3d.utility.Vector3dVector(points)
#     pc.paint_uniform_color(color)
#     return pc


# def segment_ground(pcd):
#     model, inliers = pcd.segment_plane(
#         distance_threshold=PLANE_DIST_THRESH,
#         ransac_n=PLANE_RANSAC_N,
#         num_iterations=PLANE_ITER
#     )
#     ground = pcd.select_by_index(inliers)
#     non_ground = pcd.select_by_index(inliers, invert=True)
#     print(f"Segmented ground ({len(inliers)} pts) vs non-ground ({len(non_ground.points)} pts)")
#     return ground, non_ground


# def extract_lane_lines(pts_xy):
#     lines=[]
#     mask=np.ones(len(pts_xy),dtype=bool)
#     while True:
#         if mask.sum()<MIN_LINE_POINTS: break
#         X=pts_xy[mask,0].reshape(-1,1);
#         y=pts_xy[mask,1]
#         ransac=RANSACRegressor(LinearRegression(), residual_threshold=LINE_DIST_THRESH)
#         ransac.fit(X,y)
#         inl= ransac.inlier_mask_
#         if inl.sum()<MIN_LINE_POINTS: break
#         slope= ransac.estimator_.coef_[0]; intercept=ransac.estimator_.intercept_
#         lines.append((slope,intercept,pts_xy[mask][inl]))
#         idxs=np.where(mask)[0][inl]; mask[idxs]=False
#     print(f"Found {len(lines)} lane lines")
#     return lines


# def visualize_lane_lines(lines):
#     vis=[]
#     for slope,intercept,pts in lines:
#         xs=np.array([pts[:,0].min(), pts[:,0].max()])
#         ys=slope*xs+intercept
#         line3d=np.vstack((xs,ys,np.zeros_like(xs))).T
#         vis.append(make_line_set(line3d, color=(0,1,0)))
#     return vis


# def overlay_geojson(pcd, folder_root):
#     visuals=[pcd]
#     for f in os.listdir(folder_root):
#         gj=os.path.join(folder_root,f,'feature.geojson')
#         if not os.path.exists(gj): continue
#         geom=json.load(open(gj))['features'][0]['geometry']
#         pts3d=transform_coords(extract_3d_coords(geom))
#         t=geom['type']
#         if t in ('LineString','MultiLineString'):
#             visuals.append(make_line_set(pts3d))
#         elif t in ('Polygon','MultiPolygon'):
#             visuals.append(make_line_set(pts3d, close_loop=True))
#             if SHAPELY_AVAILABLE:
#                 try:
#                     poly2d=ShapelyPolygon(pts3d[:,:2]); tris=triangulate(poly2d)
#                     verts=[]; faces=[]; idx=0; z0=pts3d[:,2].mean()
#                     for tri in tris:
#                         coords2d=list(tri.exterior.coords)[:-1]
#                         face=[]
#                         for x,y in coords2d:
#                             verts.append([x,y,z0]); face.append(idx); idx+=1
#                         faces.append(face)
#                     mesh=o3d.geometry.TriangleMesh(
#                         vertices=o3d.utility.Vector3dVector(np.array(verts)),
#                         triangles=o3d.utility.Vector3iVector(np.array(faces))
#                     ); mesh.paint_uniform_color((1,0,0)); visuals.append(mesh)
#                 except Exception as e:
#                     print(f"Fill error: {e}")
#         else:
#             visuals.append(make_point_cloud(pts3d))
#     return visuals


# def main():
#     # 1) Load single LAS and overlay geojson
#     pcd=load_las_as_pcd(LAS_PATH)
#     visuals=overlay_geojson(pcd, FEATURE_ROOT)

#     # 2) Segment ground & extract lanes
#     ground, non_ground=segment_ground(pcd)
#     visuals.append(ground.paint_uniform_color([0.6,0.6,0.6]))
#     lines=extract_lane_lines(np.asarray(non_ground.points)[:,:2])
#     visuals.extend(visualize_lane_lines(lines))

#     # 3) Visualize all
#     print("Rendering final result...")
#     o3d.visualization.draw_geometries(visuals)

# if __name__=='__main__':
#     main()

#------------------------------------------------------------------------
# downnsample code
#------------------------------------------------------------------------

# import os
# import json
# import numpy as np
# import open3d as o3d
# import laspy
# from pyproj import Transformer
# from sklearn.linear_model import RANSACRegressor, LinearRegression

# # Optional: for filled GeoJSON polygons
# try:
#     from shapely.geometry import Polygon as ShapelyPolygon
#     from shapely.ops import triangulate
#     SHAPELY_AVAILABLE = True
# except ImportError:
#     SHAPELY_AVAILABLE = False

# # ---------------- CONFIG ----------------
# # Single LAS file path
# LAS_PATH = r"C:\Users\<USER>\Desktop\HERE-Inputs\only_las_files\HT412_1738935654_3217135_1422974242419323_1422974299193520.las"
# # Folder containing GeoJSON features (each in a subfolder)
# FEATURE_ROOT = r"C:\Users\<USER>\Desktop\HERE-Inputs\features_split_new"

# # Voxel size for downsampling (meters)
# VOXEL_SIZE = 0.05

# # CRS transformation: GeoJSON EPSG:4326 → LAS EPSG:25832
# TRANSFORMER = Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

# # RANSAC settings
# PLANE_DIST_THRESH = 0.2   # meters
# PLANE_RANSAC_N = 3
# PLANE_ITER = 300          # reduced iterations for speed
# LINE_DIST_THRESH = 0.1    # meters
# MIN_LINE_POINTS = 200
# # ----------------------------------------

# def load_las_as_pcd(las_path):
#     """
#     Read a LAS file, vectorized transform to target CRS, return Open3D PointCloud.
#     """
#     print(f"🔍 Loading LAS file: {las_path}")
#     las = laspy.read(las_path)
#     # Vectorized transformation
#     xs, ys, zs = las.x, las.y, las.z
#     x2, y2, z2 = TRANSFORMER.transform(xs, ys, zs)
#     points = np.vstack((x2, y2, z2)).T
#     pcd = o3d.geometry.PointCloud()
#     pcd.points = o3d.utility.Vector3dVector(points)
#     return pcd


# def transform_coords(coords):
#     """
#     Transform list of (lon, lat, z) in EPSG:4326 to EPSG:25832.
#     """
#     return np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in coords])


# def extract_3d_coords(geometry):
#     """Flatten GeoJSON geometry to list of (x,y,z) triples."""
#     t = geometry.get("type", "")
#     coords = geometry.get("coordinates", [])
#     if t == "LineString":
#         return coords
#     if t == "MultiLineString":
#         return [pt for line in coords for pt in line]
#     if t == "Polygon":
#         return coords[0]
#     if t == "MultiPolygon":
#         pts = []
#         for poly in coords:
#             pts.extend(poly[0])
#         return pts
#     return []


# def make_line_set(points, close_loop=False, color=(1,0,0)):
#     pts = o3d.utility.Vector3dVector(points)
#     lines = [[i, i+1] for i in range(len(points)-1)]
#     if close_loop and len(points) > 2:
#         lines.append([len(points)-1, 0])
#     ls = o3d.geometry.LineSet(points=pts,
#                                lines=o3d.utility.Vector2iVector(lines))
#     ls.colors = o3d.utility.Vector3dVector([color] * len(lines))
#     return ls


# def make_point_cloud(points, color=(1,0,0)):
#     pc = o3d.geometry.PointCloud()
#     pc.points = o3d.utility.Vector3dVector(points)
#     pc.paint_uniform_color(color)
#     return pc


# def overlay_geojson(pcd, folder_root):
#     """
#     Overlay all GeoJSON features on the given point cloud.
#     Returns list of geometries (pcd + overlays).
#     """
#     visuals = [pcd]
#     for folder in os.listdir(folder_root):
#         gj = os.path.join(folder_root, folder, 'feature.geojson')
#         if not os.path.exists(gj):
#             continue
#         try:
#             feat = json.load(open(gj))['features'][0]
#         except Exception as e:
#             print(f"⚠️ Could not read {gj}: {e}")
#             continue
#         geom = feat.get('geometry', {})
#         raw = extract_3d_coords(geom)
#         if not raw:
#             continue
#         pts3d = transform_coords(raw)
#         t = geom.get('type', '')
#         if t in ('LineString','MultiLineString'):
#             visuals.append(make_line_set(pts3d))
#         elif t in ('Polygon','MultiPolygon'):
#             visuals.append(make_line_set(pts3d, close_loop=True))
#             if SHAPELY_AVAILABLE:
#                 try:
#                     from shapely.geometry import Polygon as ShapelyPolygon
#                     from shapely.ops import triangulate
#                     poly2d = ShapelyPolygon(pts3d[:,:2])
#                     tris = triangulate(poly2d)
#                     verts, faces = [], []
#                     z0 = pts3d[:,2].mean()
#                     idx = 0
#                     for tri in tris:
#                         coords2d = list(tri.exterior.coords)[:-1]
#                         face = []
#                         for x,y in coords2d:
#                             verts.append([x,y,z0])
#                             face.append(idx)
#                             idx += 1
#                         faces.append(face)
#                     mesh = o3d.geometry.TriangleMesh(
#                         vertices=o3d.utility.Vector3dVector(np.array(verts)),
#                         triangles=o3d.utility.Vector3iVector(np.array(faces))
#                     )
#                     mesh.paint_uniform_color((1,0,0))
#                     visuals.append(mesh)
#                 except Exception as e:
#                     print(f"⚠️ Fill failed: {e}")
#         else:
#             visuals.append(make_point_cloud(pts3d))
#     return visuals


# def segment_ground(pcd):
#     """RANSAC plane segmentation on given pcd."""
#     plane_model, inliers = pcd.segment_plane(
#         distance_threshold=PLANE_DIST_THRESH,
#         ransac_n=PLANE_RANSAC_N,
#         num_iterations=PLANE_ITER
#     )
#     ground = pcd.select_by_index(inliers)
#     non_ground = pcd.select_by_index(inliers, invert=True)
#     print(f"Ground pts={len(inliers)}, Non-ground pts={len(non_ground.points)}")
#     return ground, non_ground


# def extract_lane_lines(pts_xy):
#     """Extract multiple lines via iterative RANSAC on XY points."""
#     lines = []
#     mask = np.ones(len(pts_xy), dtype=bool)
#     while mask.sum() >= MIN_LINE_POINTS:
#         X = pts_xy[mask,0].reshape(-1,1)
#         y = pts_xy[mask,1]
#         ransac = RANSACRegressor(LinearRegression(),
#                                  residual_threshold=LINE_DIST_THRESH)
#         ransac.fit(X, y)
#         inl = ransac.inlier_mask_
#         if inl.sum() < MIN_LINE_POINTS:
#             break
#         slope = float(ransac.estimator_.coef_[0])
#         intercept = float(ransac.estimator_.intercept_)
#         lines.append((slope, intercept, pts_xy[mask][inl]))
#         idxs = np.where(mask)[0][inl]
#         mask[idxs] = False
#     print(f"Extracted {len(lines)} lane lines")
#     return lines


# def visualize_lane_lines(lines):
#     vis = []
#     for slope, intercept, pts in lines:
#         xs = np.array([pts[:,0].min(), pts[:,0].max()])
#         ys = slope * xs + intercept
#         # assume ground plane at z=0 for visualization
#         line3d = np.vstack((xs, ys, np.zeros_like(xs))).T
#         vis.append(make_line_set(line3d, color=(0,1,0)))
#     return vis


# def main():
#     # 1) Load LAS and downsample
#     full_pcd = load_las_as_pcd(LAS_PATH)
#     pcd_down = full_pcd.voxel_down_sample(voxel_size=VOXEL_SIZE)

#     # Optional: crop to region of interest
#     # bbox = o3d.geometry.AxisAlignedBoundingBox(min_bound=[x0,y0,z0], max_bound=[x1,y1,z1])
#     # pcd_proc = pcd_down.crop(bbox)
#     pcd_proc = pcd_down

#     # 2) Overlay GeoJSON on downsampled cloud
#     visuals = overlay_geojson(pcd_proc, FEATURE_ROOT)

#     # 3) Ground segmentation & lane extraction
#     ground, non_ground = segment_ground(pcd_proc)
#     visuals.append(ground.paint_uniform_color([0.6,0.6,0.6]))
#     pts_xy = np.asarray(non_ground.points)[:,:2]
#     lines = extract_lane_lines(pts_xy)
#     visuals.extend(visualize_lane_lines(lines))

#     # 4) Render all
#     print("🎨 Rendering final result…")
#     o3d.visualization.draw_geometries(visuals)

# if __name__ == '__main__':
#     main()

#--------------------------------------------------------------
# more downsampled
#--------------------------------------------------------------

# import os
# import json
# import numpy as np
# import open3d as o3d
# import laspy
# from pyproj import Transformer
# from sklearn.linear_model import RANSACRegressor, LinearRegression

# # Optional: for filled GeoJSON polygons
# try:
#     from shapely.geometry import Polygon as ShapelyPolygon
#     from shapely.ops import triangulate
#     SHAPELY_AVAILABLE = True
# except ImportError:
#     SHAPELY_AVAILABLE = False

# # ---------------- CONFIG ----------------
# # Single LAS file path
# LAS_PATH = r"C:\Users\<USER>\Desktop\HERE-Inputs\only_las_files\HT412_1738935654_3217135_1422974242419323_1422974299193520.las"
# # Folder containing GeoJSON features (each in a subfolder)
# FEATURE_ROOT = r"C:\Users\<USER>\Desktop\HERE-Inputs\features_split_new"

# # Voxel size for downsampling (meters)
# VOXEL_SIZE = 0.05
# # Margin (meters) to pad ROI around features
# ROI_MARGIN = 10.0

# # CRS transformation: GeoJSON EPSG:4326 → LAS EPSG:25832
# TRANSFORMER = Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

# # RANSAC settings
# PLANE_DIST_THRESH = 0.2   # meters
# PLANE_RANSAC_N = 3
# PLANE_ITER = 300          # reduced iterations for speed
# LINE_DIST_THRESH = 0.1    # meters
# MIN_LINE_POINTS = 200
# # ----------------------------------------

# def extract_3d_coords(geometry):
#     """Flatten GeoJSON geometry to list of (lon, lat, z) triples."""
#     t = geometry.get("type", "")
#     coords = geometry.get("coordinates", [])
#     if t == "LineString":
#         return coords
#     if t == "MultiLineString":
#         return [pt for line in coords for pt in line]
#     if t == "Polygon":
#         return coords[0]
#     if t == "MultiPolygon":
#         pts = []
#         for poly in coords:
#             pts.extend(poly[0])
#         return pts
#     return []


# def compute_feature_roi(folder_root, margin=ROI_MARGIN):
#     """
#     Compute 2D ROI bounding box around all GeoJSON features (in target CRS), with padding.
#     Returns (xmin, ymin, xmax, ymax).
#     """
#     all_pts = []
#     for f in os.listdir(folder_root):
#         gj = os.path.join(folder_root, f, 'feature.geojson')
#         if not os.path.exists(gj):
#             continue
#         feat = json.load(open(gj))['features'][0]
#         raw = extract_3d_coords(feat['geometry'])
#         if not raw:
#             continue
#         pts = np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in raw])
#         all_pts.append(pts[:, :2])
#     if not all_pts:
#         raise RuntimeError("No GeoJSON points found for ROI computation")
#     pts2d = np.vstack(all_pts)
#     xmin, ymin = pts2d.min(axis=0) - margin
#     xmax, ymax = pts2d.max(axis=0) + margin
#     return xmin, ymin, xmax, ymax


# def load_las_roi_as_pcd(las_path, roi_bounds=None):
#     """
#     Read LAS, vectorized transform, and optionally crop to 2D ROI.
#     Returns Open3D PointCloud.
#     """
#     print(f"🔍 Loading LAS file: {las_path}")
#     las = laspy.read(las_path)
#     xs, ys, zs = las.x, las.y, las.z
#     x2, y2, z2 = TRANSFORMER.transform(xs, ys, zs)
#     pts = np.vstack((x2, y2, z2)).T
#     if roi_bounds:
#         xmin, ymin, xmax, ymax = roi_bounds
#         mask = (pts[:,0]>=xmin) & (pts[:,0]<=xmax) & (pts[:,1]>=ymin) & (pts[:,1]<=ymax)
#         pts = pts[mask]
#         print(f"🔪 Cropped to ROI: {len(pts)} points remain")
#     pcd = o3d.geometry.PointCloud()
#     pcd.points = o3d.utility.Vector3dVector(pts)
#     return pcd


# def make_line_set(points, close_loop=False, color=(1,0,0)):
#     pts = o3d.utility.Vector3dVector(points)
#     lines = [[i, i+1] for i in range(len(points)-1)]
#     if close_loop and len(points)>2:
#         lines.append([len(points)-1,0])
#     ls = o3d.geometry.LineSet(points=pts,
#                                lines=o3d.utility.Vector2iVector(lines))
#     ls.colors = o3d.utility.Vector3dVector([color]*len(lines))
#     return ls


# def overlay_geojson(pcd, folder_root):
#     visuals = [pcd]
#     for f in os.listdir(folder_root):
#         gj = os.path.join(folder_root, f, 'feature.geojson')
#         if not os.path.exists(gj):
#             continue
#         feat = json.load(open(gj))['features'][0]
#         raw = extract_3d_coords(feat['geometry'])
#         if not raw:
#             continue
#         pts3d = np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in raw])
#         t = feat['geometry']['type']
#         if t in ('LineString','MultiLineString'):
#             visuals.append(make_line_set(pts3d))
#         elif t in ('Polygon','MultiPolygon'):
#             visuals.append(make_line_set(pts3d, close_loop=True))
#         else:
#             pc = o3d.geometry.PointCloud()
#             pc.points = o3d.utility.Vector3dVector(pts3d)
#             pc.paint_uniform_color((1,0,0))
#             visuals.append(pc)
#     return visuals


# def segment_ground(pcd):
#     plane_model, inliers = pcd.segment_plane(
#         distance_threshold=PLANE_DIST_THRESH,
#         ransac_n=PLANE_RANSAC_N,
#         num_iterations=PLANE_ITER
#     )
#     ground = pcd.select_by_index(inliers)
#     non_ground = pcd.select_by_index(inliers, invert=True)
#     print(f"Ground pts={len(inliers)}, Non-ground pts={len(non_ground.points)}")
#     return ground, non_ground


# def extract_lane_lines(pts_xy):
#     lines=[]
#     mask=np.ones(len(pts_xy),dtype=bool)
#     while mask.sum()>=MIN_LINE_POINTS:
#         X=pts_xy[mask,0].reshape(-1,1)
#         y=pts_xy[mask,1]
#         r = RANSACRegressor(LinearRegression(), residual_threshold=LINE_DIST_THRESH)
#         r.fit(X,y)
#         inl=r.inlier_mask_
#         if inl.sum()<MIN_LINE_POINTS: break
#         m=float(r.estimator_.coef_[0]); b=float(r.estimator_.intercept_)
#         lines.append((m,b,pts_xy[mask][inl]))
#         idxs=np.where(mask)[0][inl]; mask[idxs]=False
#     print(f"Extracted {len(lines)} lane lines")
#     return lines


# def visualize_lane_lines(lines):
#     vis=[]
#     for m,b,pts in lines:
#         xs=np.array([pts[:,0].min(), pts[:,0].max()])
#         ys=m*xs+b
#         line3d=np.vstack((xs,ys,np.zeros_like(xs))).T
#         vis.append(make_line_set(line3d, color=(0,1,0)))
#     return vis


# def main():
#     # 1) Compute ROI from features
#     roi = compute_feature_roi(FEATURE_ROOT)
#     print(f"📐 Using ROI bounds: {roi}")

#     # 2) Load LAS, crop to ROI, downsample
#     full_pcd = load_las_roi_as_pcd(LAS_PATH, roi_bounds=roi)
#     pcd_down = full_pcd.voxel_down_sample(voxel_size=VOXEL_SIZE)
#     print(f"🔽 Downsampled to {len(pcd_down.points)} points")

#     # 3) Overlay GeoJSON on cropped, downsampled cloud
#     visuals = overlay_geojson(pcd_down, FEATURE_ROOT)

#     # 4) Segment ground & extract lanes
#     ground, non_ground = segment_ground(pcd_down)
#     visuals.append(ground.paint_uniform_color([0.6,0.6,0.6]))
#     pts_xy = np.asarray(non_ground.points)[:,:2]
#     lines = extract_lane_lines(pts_xy)
#     visuals.extend(visualize_lane_lines(lines))

#     # 5) Render
#     print("🎨 Rendering final result…")
#     o3d.visualization.draw_geometries(visuals)

# if __name__=='__main__':
#     main()

#------------------------------------------------------------
# direct pcd input
#------------------------------------------------------------
# import os
# import json
# import numpy as np
# import open3d as o3d
# from pyproj import Transformer
# from sklearn.linear_model import RANSACRegressor, LinearRegression

# # Optional: for filled GeoJSON polygons
# try:
#     from shapely.geometry import Polygon as ShapelyPolygon
#     from shapely.ops import triangulate
#     SHAPELY_AVAILABLE = True
# except ImportError:
#     SHAPELY_AVAILABLE = False

# # ---------------- CONFIG ----------------
# # Path to your PCD file
# PCD_PATH = r"C:\Users\<USER>\Desktop\HERE-Inputs\merged_transformed.pcd"
# # Folder containing GeoJSON features (each in a subfolder)
# FEATURE_ROOT = r"C:\Users\<USER>\Desktop\HERE-Inputs\features_split_new"

# # Voxel size for downsampling (meters)
# VOXEL_SIZE = 0.5

# # CRS transformation: GeoJSON (EPSG:4326) → PCD CRS (EPSG:25832)
# TRANSFORMER = Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

# # RANSAC settings for ground and lane extraction
# PLANE_DIST_THRESH = 0.2   # meters
# PLANE_RANSAC_N = 3
# PLANE_ITER = 300          # reduced iterations for speed
# LINE_DIST_THRESH = 0.1    # meters
# MIN_LINE_POINTS = 200
# # ----------------------------------------

# def extract_3d_coords(geometry):
#     """Flatten GeoJSON geometry to list of (lon, lat, z) triples."""
#     t = geometry.get("type", "")
#     coords = geometry.get("coordinates", [])
#     if t == "LineString":
#         return coords
#     if t == "MultiLineString":
#         return [pt for line in coords for pt in line]
#     if t == "Polygon":
#         return coords[0]
#     if t == "MultiPolygon":
#         pts = []
#         for poly in coords:
#             pts.extend(poly[0])
#         return pts
#     return []


# def make_line_set(points, close_loop=False, color=(1, 0, 0)):
#     """Build an Open3D LineSet from Nx3 numpy array."""
#     pts_o3d = o3d.utility.Vector3dVector(points)
#     lines = [[i, i+1] for i in range(len(points)-1)]
#     if close_loop and len(points) > 2:
#         lines.append([len(points)-1, 0])
#     ls = o3d.geometry.LineSet(
#         points=pts_o3d,
#         lines=o3d.utility.Vector2iVector(lines)
#     )
#     ls.colors = o3d.utility.Vector3dVector([color] * len(lines))
#     return ls


# def overlay_geojson(pcd, folder_root):
#     """Overlay all GeoJSON features on given point cloud."""
#     visuals = [pcd]
#     for folder in os.listdir(folder_root):
#         gj = os.path.join(folder_root, folder, 'feature.geojson')
#         if not os.path.exists(gj):
#             continue
#         try:
#             feat = json.load(open(gj))['features'][0]
#         except Exception as e:
#             print(f"⚠️ Could not read {gj}: {e}")
#             continue
#         raw = extract_3d_coords(feat['geometry'])
#         if not raw:
#             continue
#         pts = np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in raw])
#         geom_type = feat['geometry']['type']
#         if geom_type in ('LineString', 'MultiLineString'):
#             visuals.append(make_line_set(pts))
#         elif geom_type in ('Polygon', 'MultiPolygon'):
#             visuals.append(make_line_set(pts, close_loop=True))
#             if SHAPELY_AVAILABLE:
#                 try:
#                     poly2d = ShapelyPolygon(pts[:, :2])
#                     tris = triangulate(poly2d)
#                     verts, faces = [], []
#                     avg_z = float(np.mean(pts[:, 2]))
#                     idx = 0
#                     for tri in tris:
#                         coords2d = list(tri.exterior.coords)[:-1]
#                         face = []
#                         for x, y in coords2d:
#                             verts.append([x, y, avg_z])
#                             face.append(idx)
#                             idx += 1
#                         faces.append(face)
#                     mesh = o3d.geometry.TriangleMesh(
#                         vertices=o3d.utility.Vector3dVector(np.array(verts)),
#                         triangles=o3d.utility.Vector3iVector(np.array(faces))
#                     )
#                     mesh.paint_uniform_color((1, 0, 0))
#                     visuals.append(mesh)
#                 except Exception as e:
#                     print(f"⚠️ Polygon fill failed: {e}")
#         else:
#             pc = o3d.geometry.PointCloud()
#             pc.points = o3d.utility.Vector3dVector(pts)
#             pc.paint_uniform_color((1, 0, 0))
#             visuals.append(pc)
#     return visuals


# def segment_ground(pcd):
#     """Perform RANSAC plane segmentation to separate ground."""
#     model, inliers = pcd.segment_plane(
#         distance_threshold=PLANE_DIST_THRESH,
#         ransac_n=PLANE_RANSAC_N,
#         num_iterations=PLANE_ITER
#     )
#     ground = pcd.select_by_index(inliers)
#     non_ground = pcd.select_by_index(inliers, invert=True)
#     print(f"Ground pts={len(inliers)}, Non-ground pts={len(non_ground.points)}")
#     return ground, non_ground


# def extract_lane_lines(pts_xy):
#     """Iterative RANSAC line fitting on 2D XY points to find lane boundaries."""
#     lines = []
#     mask = np.ones(len(pts_xy), dtype=bool)
#     while mask.sum() >= MIN_LINE_POINTS:
#         X = pts_xy[mask, 0].reshape(-1, 1)
#         y = pts_xy[mask, 1]
#         ransac = RANSACRegressor(LinearRegression(), residual_threshold=LINE_DIST_THRESH)
#         ransac.fit(X, y)
#         inlier_mask = ransac.inlier_mask_
#         if inlier_mask.sum() < MIN_LINE_POINTS:
#             break
#         slope = float(ransac.estimator_.coef_[0])
#         intercept = float(ransac.estimator_.intercept_)
#         lines.append((slope, intercept, pts_xy[mask][inlier_mask]))
#         idxs = np.where(mask)[0][inlier_mask]
#         mask[idxs] = False
#     print(f"Extracted {len(lines)} lane lines")
#     return lines


# def visualize_lane_lines(lines):
#     """Build LineSets from fitted lane lines."""
#     visuals = []
#     for slope, intercept, pts in lines:
#         xs = np.array([pts[:,0].min(), pts[:,0].max()])
#         ys = slope * xs + intercept
#         line3d = np.vstack((xs, ys, np.zeros_like(xs))).T
#         visuals.append(make_line_set(line3d, color=(0, 1, 0)))
#     return visuals


# def clean_point_cloud(pcd):
#     """Remove points with infinite values from point cloud."""
#     points = np.asarray(pcd.points)
#     # Find points that don't have infinite values
#     mask = ~np.any(np.isinf(points), axis=1)
#     # Create new point cloud with only finite points
#     clean_pcd = o3d.geometry.PointCloud()
#     clean_pcd.points = o3d.utility.Vector3dVector(points[mask])
#     return clean_pcd


# def main():
#     # 1) Load the PCD file
#     print(f"🔍 Loading PCD file: {PCD_PATH}")
#     pcd = o3d.io.read_point_cloud(PCD_PATH)
#     print(f"Initial number of points: {len(pcd.points)}")
    
#     # Clean point cloud
#     pcd = clean_point_cloud(pcd)
#     print(f"Points after removing infinites: {len(pcd.points)}")
    
#     # Print point cloud information
#     bbox = pcd.get_axis_aligned_bounding_box()
#     extent = bbox.get_extent()
#     print(f"Point cloud bounds: {bbox}")
#     print(f"Point cloud extent: {extent}")
    
#     # Calculate appropriate voxel size based on point cloud extent
#     voxel_size = max(extent) / 100  # Use 1/100th of the largest dimension
#     print(f"Using voxel size: {voxel_size}")

#     # 2) Downsample for speed
#     pcd_down = pcd.voxel_down_sample(voxel_size=voxel_size)
#     print(f"🔽 Downsampled to {len(pcd_down.points)} points")

#     # 3) Overlay GeoJSON features
#     visuals = overlay_geojson(pcd_down, FEATURE_ROOT)

#     # 4) Ground segmentation and lane extraction
#     ground, non_ground = segment_ground(pcd_down)
#     visuals.append(ground.paint_uniform_color([0.6, 0.6, 0.6]))
#     pts_xy = np.asarray(non_ground.points)[:, :2]
#     lines = extract_lane_lines(pts_xy)
#     visuals.extend(visualize_lane_lines(lines))

#     # 5) Render
#     print("🎨 Rendering final result…")
#     o3d.visualization.draw_geometries(visuals)

# if __name__ == '__main__':
#     main()

#-------------------------------------------------------------------
# GEOJSON RANSAC RUN - WORKING TESTED AND GOT LINES
#-------------------------------------------------------------------

import os
import json
import numpy as np
import open3d as o3d
import laspy
from pyproj import Transformer

# ---------------- CONFIG ----------------
# Path to your LAS file (or change to PCD loader if needed)
INPUT_LAS = r"C:\Users\<USER>\Desktop\HERE-Inputs\only_las_files\HT412_1738935654_3217135_1422974524271725_1422974554105924.las"
# Directory containing subfolders with feature.geojson
FEATURE_ROOT = r"C:\Users\<USER>\Desktop\HERE-Inputs\features_split_new"

# GeoJSON is in EPSG:4326, LAS/PCD in EPSG:25832
TRANSFORMER = Transformer.from_crs("EPSG:4326", "EPSG:25832", always_xy=True)

# Visualization colors
LANE_COLOR = (0, 1, 0)  # green for lane boundaries
BASE_COLOR = (0.5, 0.5, 0.5)  # gray for base point cloud
# ----------------------------------------

def load_las_as_pcd(las_path):
    """
    Read a .las file, apply vectorized CRS transform, return Open3D PointCloud.
    """
    print(f"🔍 Loading LAS file: {las_path}")
    las = laspy.read(las_path)
    xs, ys, zs = las.x, las.y, las.z
    x2, y2, z2 = TRANSFORMER.transform(xs, ys, zs)
    pts = np.vstack((x2, y2, z2)).T
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts)
    pcd.paint_uniform_color(BASE_COLOR)
    return pcd


def extract_coords_from_geojson_geometry(geom):
    """
    Given a GeoJSON geometry dict, return a flat list of (lon, lat, z) tuples.
    Supports LineString, MultiLineString, Polygon, MultiPolygon.
    """
    t = geom.get("type", "")
    coords = geom.get("coordinates", [])
    if t == "LineString":
        return coords
    if t == "MultiLineString":
        return [pt for line in coords for pt in line]
    if t == "Polygon":
        return coords[0]  # outer ring
    if t == "MultiPolygon":
        pts = []
        for poly in coords:
            pts.extend(poly[0])
        return pts
    return []


def make_line_set(points, color):
    """Construct an Open3D LineSet from an Nx3 array of points."""
    pts_v3d = o3d.utility.Vector3dVector(points)
    # connect sequential points
    lines = [[i, i+1] for i in range(len(points)-1)]
    ls = o3d.geometry.LineSet(
        points=pts_v3d,
        lines=o3d.utility.Vector2iVector(lines)
    )
    ls.colors = o3d.utility.Vector3dVector([color] * len(lines))
    return ls


def main():
    # 1) Load the base point cloud
    base_pcd = load_las_as_pcd(INPUT_LAS)
    visuals = [base_pcd]

    # 2) Loop over each feature.geojson
    for folder in os.listdir(FEATURE_ROOT):
        gj_path = os.path.join(FEATURE_ROOT, folder, 'feature.geojson')
        if not os.path.exists(gj_path):
            continue
        try:
            data = json.load(open(gj_path))
            feat = data['features'][0]
        except Exception as e:
            print(f"⚠️ Failed to load {gj_path}: {e}")
            continue

        props = feat.get('properties', {})
        boundaries = props.get('laneBoundaries', [])
        if not boundaries:
            continue  # no laneBoundaries array

        # 3) For each lane boundary entry
        for lb in boundaries:
            geom = lb.get('geometry', {})
            raw = extract_coords_from_geojson_geometry(geom)
            if not raw:
                continue
            # transform to LAS CRS
            pts = np.array([TRANSFORMER.transform(lon, lat, z) for lon, lat, z in raw])
            # create LineSet
            ls = make_line_set(pts, color=LANE_COLOR)
            visuals.append(ls)
        print(f"✔️  Overlayed {len(boundaries)} lane boundaries from {folder}")

    # 4) Visualize all
    print("🎨 Rendering lane boundaries overlay…")
    o3d.visualization.draw_geometries(visuals)

if __name__ == '__main__':
    main()
