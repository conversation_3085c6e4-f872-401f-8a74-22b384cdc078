#!/usr/bin/env python3
"""
Clean Lane Detection - Focus only on actual road lane markings
Removes noise and vegetation, detects only painted lane lines
"""

import json
import numpy as np
import open3d as o3d
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from scipy.spatial import cKDTree
from pyproj import Transformer
import laspy
from pathlib import Path
import argparse

# Strict parameters for clean lane detection
INTENSITY_THRESHOLD_PERCENTILE = 95  # Only very bright points (painted lines)
HEIGHT_ABOVE_GROUND_MIN = 0.02       # Very close to road surface
HEIGHT_ABOVE_GROUND_MAX = 0.15       # Maximum height for road markings
DBSCAN_EPS = 0.08                    # Tighter clustering
DBSCAN_MIN_SAMPLES = 20              # More points required per cluster
MIN_LINE_LENGTH = 10.0               # Longer minimum line length
MAX_LINE_WIDTH = 0.5                 # Maximum width of a lane line
MIN_LINEARITY = 0.8                  # Minimum linearity score (0-1)
MAX_POINTS_PROCESS = 500000          # Smaller processing set for speed

def load_and_filter_las(las_path: Path) -> tuple[np.ndarray, np.ndarray]:
    """Load LAS and apply initial filtering."""
    print(f"Loading LAS file: {las_path}")
    
    las = laspy.read(str(las_path))
    points = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
    intensity = las.intensity.astype(np.float32)
    
    print(f"Loaded {len(points):,} points")
    
    # Downsample if too many points
    if len(points) > MAX_POINTS_PROCESS:
        np.random.seed(42)
        indices = np.random.choice(len(points), MAX_POINTS_PROCESS, replace=False)
        points = points[indices]
        intensity = intensity[indices]
        print(f"Downsampled to {len(points):,} points")
    
    return points, intensity

def estimate_road_surface(points: np.ndarray) -> tuple[np.ndarray, float]:
    """Estimate road surface using robust plane fitting."""
    print("Estimating road surface...")
    
    # Use lowest 20% of points to find road surface
    z_values = points[:, 2]
    low_z_threshold = np.percentile(z_values, 20)
    road_candidates = points[z_values <= low_z_threshold]
    
    # RANSAC plane fitting on road candidates
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(road_candidates)
    
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=0.05,
        ransac_n=3,
        num_iterations=2000
    )
    
    normal = np.array(plane_model[:3])
    offset = plane_model[3]
    
    print(f"Road surface: {normal[0]:.3f}x + {normal[1]:.3f}y + {normal[2]:.3f}z + {offset:.3f} = 0")
    return normal, offset

def filter_lane_candidates(points: np.ndarray, intensity: np.ndarray, 
                          road_normal: np.ndarray, road_offset: float) -> np.ndarray:
    """Filter points to only potential lane markings."""
    
    # Height above road surface
    heights = np.abs(points @ road_normal + road_offset) / np.linalg.norm(road_normal)
    
    # Very high intensity threshold (top 5% brightest points)
    intensity_threshold = np.percentile(intensity, INTENSITY_THRESHOLD_PERCENTILE)
    
    # Strict filtering criteria
    height_mask = (heights >= HEIGHT_ABOVE_GROUND_MIN) & (heights <= HEIGHT_ABOVE_GROUND_MAX)
    intensity_mask = intensity >= intensity_threshold
    
    # Combine filters
    lane_mask = height_mask & intensity_mask
    lane_points = points[lane_mask]
    
    print(f"Found {len(lane_points):,} potential lane marking points")
    print(f"Intensity threshold: {intensity_threshold:.1f}")
    
    return lane_points

def cluster_lane_points(points: np.ndarray) -> list[np.ndarray]:
    """Cluster lane points with strict parameters."""
    if len(points) == 0:
        return []
    
    print(f"Clustering {len(points):,} lane points...")
    
    # Use only X,Y coordinates for clustering
    xy_points = points[:, :2]
    
    # Standardize coordinates
    scaler = StandardScaler()
    xy_scaled = scaler.fit_transform(xy_points)
    
    # Strict DBSCAN clustering
    clustering = DBSCAN(eps=DBSCAN_EPS, min_samples=DBSCAN_MIN_SAMPLES)
    labels = clustering.fit_predict(xy_scaled)
    
    # Extract clusters
    clusters = []
    unique_labels = set(labels)
    if -1 in unique_labels:
        unique_labels.remove(-1)  # Remove noise
    
    for label in unique_labels:
        cluster_points = points[labels == label]
        clusters.append(cluster_points)
    
    print(f"Found {len(clusters)} initial clusters")
    return clusters

def validate_lane_cluster(cluster: np.ndarray) -> bool:
    """Validate if cluster represents a real lane line."""
    if len(cluster) < DBSCAN_MIN_SAMPLES:
        return False
    
    xy = cluster[:, :2]
    
    # Check line length
    distances = np.linalg.norm(np.diff(xy, axis=0), axis=1)
    total_length = np.sum(distances)
    if total_length < MIN_LINE_LENGTH:
        return False
    
    # Check linearity using PCA
    centered = xy - xy.mean(axis=0)
    cov_matrix = np.cov(centered.T)
    eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)
    
    # Linearity score: ratio of largest to smallest eigenvalue
    if eigenvals[0] > 0:
        linearity = eigenvals[1] / eigenvals[0]
    else:
        linearity = 0
    
    if linearity < MIN_LINEARITY:
        return False
    
    # Check width (spread perpendicular to main direction)
    principal_direction = eigenvecs[:, -1]
    perpendicular_direction = np.array([-principal_direction[1], principal_direction[0]])
    
    # Project points onto perpendicular direction
    perp_projections = centered @ perpendicular_direction
    width = np.max(perp_projections) - np.min(perp_projections)
    
    if width > MAX_LINE_WIDTH:
        return False
    
    return True

def fit_clean_line(cluster: np.ndarray) -> np.ndarray:
    """Fit a clean line to validated cluster."""
    xy = cluster[:, :2]
    
    # PCA to find principal direction
    centered = xy - xy.mean(axis=0)
    cov_matrix = np.cov(centered.T)
    eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)
    principal_direction = eigenvecs[:, -1]
    
    # Project points onto principal axis
    projections = centered @ principal_direction
    sort_indices = np.argsort(projections)
    sorted_points = cluster[sort_indices]
    
    # Sample evenly along the line
    n_samples = min(50, len(sorted_points))
    if n_samples < len(sorted_points):
        indices = np.linspace(0, len(sorted_points)-1, n_samples, dtype=int)
        line_points = sorted_points[indices]
    else:
        line_points = sorted_points
    
    return line_points

def convert_to_wgs84(points: np.ndarray, source_crs: str = "EPSG:32633") -> list[list[float]]:
    """Convert points to WGS84 coordinates."""
    try:
        transformer = Transformer.from_crs(source_crs, "EPSG:4326", always_xy=True)
        coords = []
        for point in points:
            lon, lat = transformer.transform(point[0], point[1])
            coords.append([lon, lat, float(point[2])])
        return coords
    except Exception as e:
        print(f"Coordinate transformation error: {e}")
        return [[float(p[0]), float(p[1]), float(p[2])] for p in points]

def create_clean_geojson(lane_lines: list[np.ndarray], source_crs: str = "EPSG:32633") -> dict:
    """Create clean GeoJSON with only validated lane lines."""
    
    features = []
    
    for i, line in enumerate(lane_lines):
        coordinates = convert_to_wgs84(line, source_crs)
        
        # Calculate line length for properties
        line_length = 0
        for j in range(1, len(line)):
            line_length += np.linalg.norm(line[j, :2] - line[j-1, :2])
        
        properties = {
            "type": "lane_marking",
            "material": "painted",
            "color": "white",
            "style": "solid",
            "id": f"lane_{i}",
            "length_m": round(line_length, 2),
            "points": len(line)
        }
        
        feature = {
            "type": "Feature",
            "properties": properties,
            "geometry": {
                "type": "LineString",
                "coordinates": coordinates
            }
        }
        features.append(feature)
    
    geojson = {
        "type": "FeatureCollection",
        "features": features,
        "crs": {
            "type": "name",
            "properties": {
                "name": "EPSG:4326"
            }
        }
    }
    
    return geojson

def main():
    parser = argparse.ArgumentParser(description="Clean lane detection from LAS files")
    parser.add_argument("input", type=Path, help="Input LAS file")
    parser.add_argument("output", type=Path, help="Output GeoJSON file")
    parser.add_argument("--input-crs", default="EPSG:25832", 
                       help="Input coordinate system")
    parser.add_argument("--target-crs", default="EPSG:32633",
                       help="Target coordinate system for processing")
    
    args = parser.parse_args()
    
    # Load and filter LAS file
    points, intensity = load_and_filter_las(args.input)
    
    # Estimate road surface
    road_normal, road_offset = estimate_road_surface(points)
    
    # Filter to lane candidates only
    lane_candidates = filter_lane_candidates(points, intensity, road_normal, road_offset)
    
    if len(lane_candidates) == 0:
        print("No lane candidates found")
        # Create empty GeoJSON
        geojson = {"type": "FeatureCollection", "features": []}
    else:
        # Cluster lane points
        clusters = cluster_lane_points(lane_candidates)
        
        # Validate and fit lines
        valid_lines = []
        for cluster in clusters:
            if validate_lane_cluster(cluster):
                line = fit_clean_line(cluster)
                valid_lines.append(line)
        
        print(f"Extracted {len(valid_lines)} clean lane lines")
        
        # Create GeoJSON
        geojson = create_clean_geojson(valid_lines, args.target_crs)
    
    # Save output
    args.output.parent.mkdir(parents=True, exist_ok=True)
    with open(args.output, 'w') as f:
        json.dump(geojson, f, indent=2)
    
    print(f"Saved {len(geojson['features'])} lane features to {args.output}")

if __name__ == "__main__":
    main()
