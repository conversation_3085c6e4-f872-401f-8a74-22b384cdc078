#!/usr/bin/env python3
"""
Improved Lane Marking Extractor with proper coordinate transformation
Uses PDAL for LAS processing and coordinate reprojection
"""

import json
import os
import subprocess
import tempfile
from pathlib import Path
from typing import List, Tu<PERSON>, Dict, Optional
import argparse

import numpy as np
import open3d as o3d
from sklearn.cluster import DBSCAN
from sklearn.preprocessing import StandardScaler
from scipy.spatial import cKDTree
from pyproj import Transformer, CRS
import laspy

# Configuration
DEFAULT_INPUT_CRS = "EPSG:4326"
DEFAULT_OUTPUT_CRS = "EPSG:32633"  # UTM Zone 33N
INTENSITY_THRESHOLD_PERCENTILE = 85
HEIGHT_THRESHOLD_MIN = 0.05  # meters above ground
HEIGHT_THRESHOLD_MAX = 0.5   # meters above ground
DBSCAN_EPS = 0.15           # clustering distance
DBSCAN_MIN_SAMPLES = 10     # minimum points per cluster
MIN_LINE_LENGTH = 5.0       # minimum line length in meters
MAX_POINTS_PROCESS = 1000000  # downsample if more points

def check_pdal_available():
    """Check if PDAL is available in the system."""
    try:
        result = subprocess.run(['pdal', '--version'], 
                              capture_output=True, text=True, timeout=10)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False

def reproject_las_with_pdal(input_path: Path, output_path: Path, 
                           in_srs: str, out_srs: str) -> bool:
    """Reproject LAS file using PDAL."""
    try:
        cmd = [
            'pdal', 'translate', str(input_path), str(output_path),
            '-f', 'filters.reprojection',
            f'--filters.reprojection.in_srs={in_srs}',
            f'--filters.reprojection.out_srs={out_srs}'
        ]
        
        print(f"Running PDAL reprojection: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("PDAL reprojection successful")
            return True
        else:
            print(f"PDAL error: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"PDAL reprojection failed: {e}")
        return False

def load_and_preprocess_las(las_path: Path, target_crs: str = None) -> Tuple[np.ndarray, np.ndarray]:
    """Load LAS file and optionally reproject coordinates."""
    print(f"Loading LAS file: {las_path}")
    
    # Try to use PDAL for reprojection if needed
    if target_crs and check_pdal_available():
        with tempfile.NamedTemporaryFile(suffix='.las', delete=False) as tmp_file:
            tmp_path = Path(tmp_file.name)
            
        try:
            # Attempt reprojection with PDAL
            if reproject_las_with_pdal(las_path, tmp_path, DEFAULT_INPUT_CRS, target_crs):
                las_path = tmp_path
            else:
                print("PDAL reprojection failed, using original coordinates")
        except Exception as e:
            print(f"Reprojection error: {e}, using original coordinates")
    
    # Load the LAS file
    las = laspy.read(str(las_path))
    points = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
    intensity = las.intensity.astype(np.float32)
    
    print(f"Loaded {len(points):,} points")
    
    # Clean up temporary file if created
    if target_crs and 'tmp_path' in locals() and tmp_path.exists():
        try:
            tmp_path.unlink()
        except:
            pass
    
    return points, intensity

def downsample_points(points: np.ndarray, intensity: np.ndarray, 
                     max_points: int) -> Tuple[np.ndarray, np.ndarray]:
    """Downsample points if too many."""
    if len(points) <= max_points:
        return points, intensity
    
    # Random downsampling
    np.random.seed(42)
    indices = np.random.choice(len(points), max_points, replace=False)
    print(f"Downsampling from {len(points):,} to {max_points:,} points")
    
    return points[indices], intensity[indices]

def estimate_ground_plane(points: np.ndarray) -> Tuple[np.ndarray, float]:
    """Estimate ground plane using RANSAC."""
    print("Estimating ground plane...")
    
    # Use Open3D for robust plane estimation
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # RANSAC plane fitting
    plane_model, inliers = pcd.segment_plane(
        distance_threshold=0.1,
        ransac_n=3,
        num_iterations=1000
    )
    
    normal = np.array(plane_model[:3])
    offset = plane_model[3]
    
    print(f"Ground plane: {normal[0]:.3f}x + {normal[1]:.3f}y + {normal[2]:.3f}z + {offset:.3f} = 0")
    return normal, offset

def classify_road_markings(points: np.ndarray, intensity: np.ndarray, 
                          ground_normal: np.ndarray, ground_offset: float) -> Tuple[np.ndarray, np.ndarray]:
    """Classify points as potential road markings."""
    
    # Height above ground plane
    heights = np.abs(points @ ground_normal + ground_offset) / np.linalg.norm(ground_normal)
    
    # High intensity points (painted markings)
    intensity_threshold = np.percentile(intensity, INTENSITY_THRESHOLD_PERCENTILE)
    high_intensity_mask = intensity >= intensity_threshold
    
    # Points at road level
    road_level_mask = (heights >= HEIGHT_THRESHOLD_MIN) & (heights <= HEIGHT_THRESHOLD_MAX)
    
    # Combine criteria for painted markings
    painted_mask = high_intensity_mask & road_level_mask
    
    # Physical markings (raised elements)
    physical_height_mask = (heights > HEIGHT_THRESHOLD_MAX) & (heights < 1.0)  # up to 1m high
    physical_mask = physical_height_mask
    
    painted_points = points[painted_mask]
    physical_points = points[physical_mask]
    
    print(f"Found {len(painted_points):,} painted marking points")
    print(f"Found {len(physical_points):,} physical marking points")
    
    return painted_points, physical_points

def cluster_markings(points: np.ndarray, eps: float = DBSCAN_EPS, 
                    min_samples: int = DBSCAN_MIN_SAMPLES) -> List[np.ndarray]:
    """Cluster marking points using DBSCAN."""
    if len(points) == 0:
        return []
    
    print(f"Clustering {len(points):,} points...")
    
    # Use only X,Y coordinates for clustering (2D)
    xy_points = points[:, :2]
    
    # Standardize coordinates for better clustering
    scaler = StandardScaler()
    xy_scaled = scaler.fit_transform(xy_points)
    
    # DBSCAN clustering
    clustering = DBSCAN(eps=eps, min_samples=min_samples)
    labels = clustering.fit_predict(xy_scaled)
    
    # Extract clusters
    clusters = []
    unique_labels = set(labels)
    if -1 in unique_labels:
        unique_labels.remove(-1)  # Remove noise points
    
    for label in unique_labels:
        cluster_points = points[labels == label]
        if len(cluster_points) >= min_samples:
            clusters.append(cluster_points)
    
    print(f"Found {len(clusters)} clusters")
    return clusters

def fit_line_to_cluster(cluster: np.ndarray) -> Optional[np.ndarray]:
    """Fit a line to a cluster of points."""
    if len(cluster) < 10:
        return None
    
    # Sort points along the principal axis
    xy = cluster[:, :2]
    
    # PCA to find principal direction
    centered = xy - xy.mean(axis=0)
    cov_matrix = np.cov(centered.T)
    eigenvals, eigenvecs = np.linalg.eigh(cov_matrix)
    principal_direction = eigenvecs[:, -1]  # Largest eigenvalue
    
    # Project points onto principal axis
    projections = centered @ principal_direction
    sort_indices = np.argsort(projections)
    
    sorted_points = cluster[sort_indices]
    
    # Sample points along the line
    n_samples = min(100, len(sorted_points))
    if n_samples < len(sorted_points):
        indices = np.linspace(0, len(sorted_points)-1, n_samples, dtype=int)
        line_points = sorted_points[indices]
    else:
        line_points = sorted_points
    
    # Check line length
    line_length = np.linalg.norm(line_points[-1, :2] - line_points[0, :2])
    if line_length < MIN_LINE_LENGTH:
        return None
    
    return line_points

def determine_marking_type(cluster: np.ndarray, is_painted: bool) -> Dict[str, str]:
    """Determine the type and properties of a marking."""
    
    # Basic classification
    material = "painted" if is_painted else "physical"
    
    # Simple heuristics for line type
    line_length = np.linalg.norm(cluster[-1, :2] - cluster[0, :2])
    
    # Estimate if dashed or solid based on point density
    expected_points = line_length / 0.1  # Expect point every 10cm
    actual_points = len(cluster)
    density_ratio = actual_points / expected_points
    
    style = "solid" if density_ratio > 0.5 else "dashed"
    
    # Color estimation (simplified)
    color = "white" if is_painted else "unknown"
    
    return {
        "material": material,
        "style": style,
        "color": color,
        "type": "lane_marking"
    }

def convert_to_wgs84(points: np.ndarray, source_crs: str) -> List[List[float]]:
    """Convert points to WGS84 coordinates for GeoJSON."""
    try:
        transformer = Transformer.from_crs(source_crs, "EPSG:4326", always_xy=True)
        
        coords = []
        for point in points:
            lon, lat = transformer.transform(point[0], point[1])
            coords.append([lon, lat, float(point[2])])
        
        return coords
    except Exception as e:
        print(f"Coordinate transformation error: {e}")
        # Fallback: assume already in WGS84
        return [[float(p[0]), float(p[1]), float(p[2])] for p in points]

def create_geojson(painted_lines: List[np.ndarray], physical_lines: List[np.ndarray], 
                  source_crs: str) -> Dict:
    """Create GeoJSON from detected lines."""
    
    features = []
    
    # Process painted markings
    for i, line in enumerate(painted_lines):
        if line is not None:
            properties = determine_marking_type(line, is_painted=True)
            properties["id"] = f"painted_{i}"
            
            coordinates = convert_to_wgs84(line, source_crs)
            
            feature = {
                "type": "Feature",
                "properties": properties,
                "geometry": {
                    "type": "LineString",
                    "coordinates": coordinates
                }
            }
            features.append(feature)
    
    # Process physical markings
    for i, line in enumerate(physical_lines):
        if line is not None:
            properties = determine_marking_type(line, is_painted=False)
            properties["id"] = f"physical_{i}"
            
            coordinates = convert_to_wgs84(line, source_crs)
            
            feature = {
                "type": "Feature",
                "properties": properties,
                "geometry": {
                    "type": "LineString",
                    "coordinates": coordinates
                }
            }
            features.append(feature)
    
    geojson = {
        "type": "FeatureCollection",
        "features": features,
        "crs": {
            "type": "name",
            "properties": {
                "name": "EPSG:4326"
            }
        }
    }
    
    return geojson

def main():
    parser = argparse.ArgumentParser(description="Extract lane markings from LAS files")
    parser.add_argument("input", type=Path, help="Input LAS file")
    parser.add_argument("output", type=Path, help="Output GeoJSON file")
    parser.add_argument("--input-crs", default=DEFAULT_INPUT_CRS, 
                       help="Input coordinate system (default: EPSG:4326)")
    parser.add_argument("--target-crs", default=DEFAULT_OUTPUT_CRS,
                       help="Target coordinate system for processing (default: EPSG:32633)")
    
    args = parser.parse_args()
    
    # Load and preprocess LAS file
    points, intensity = load_and_preprocess_las(args.input, args.target_crs)
    
    # Downsample if necessary
    if len(points) > MAX_POINTS_PROCESS:
        points, intensity = downsample_points(points, intensity, MAX_POINTS_PROCESS)
    
    # Estimate ground plane
    ground_normal, ground_offset = estimate_ground_plane(points)
    
    # Classify road markings
    painted_points, physical_points = classify_road_markings(
        points, intensity, ground_normal, ground_offset)
    
    # Cluster and fit lines
    painted_clusters = cluster_markings(painted_points)
    physical_clusters = cluster_markings(physical_points)
    
    painted_lines = [fit_line_to_cluster(cluster) for cluster in painted_clusters]
    physical_lines = [fit_line_to_cluster(cluster) for cluster in physical_clusters]
    
    # Filter out None results
    painted_lines = [line for line in painted_lines if line is not None]
    physical_lines = [line for line in physical_lines if line is not None]
    
    print(f"Extracted {len(painted_lines)} painted lines and {len(physical_lines)} physical lines")
    
    # Create GeoJSON
    geojson = create_geojson(painted_lines, physical_lines, args.target_crs)
    
    # Save output
    args.output.parent.mkdir(parents=True, exist_ok=True)
    with open(args.output, 'w') as f:
        json.dump(geojson, f, indent=2)
    
    print(f"Saved {len(geojson['features'])} features to {args.output}")

if __name__ == "__main__":
    main()
