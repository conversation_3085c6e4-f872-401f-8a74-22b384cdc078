#!/usr/bin/env python3
"""
Verify the converted PCD file and display basic information.
"""

import open3d as o3d
import numpy as np

def verify_pcd_file(pcd_file_path: str):
    """Verify and display information about a PCD file."""
    
    try:
        print(f"Loading PCD file: {pcd_file_path}")
        pcd = o3d.io.read_point_cloud(pcd_file_path)
        
        print("=" * 60)
        print("PCD File Information:")
        print("=" * 60)
        
        # Basic information
        points = np.asarray(pcd.points)
        print(f"Number of points: {len(points)}")
        print(f"Point cloud dimensions: {points.shape}")
        
        # Coordinate bounds
        print(f"\nCoordinate bounds:")
        print(f"  X: {points[:, 0].min():.6f} to {points[:, 0].max():.6f}")
        print(f"  Y: {points[:, 1].min():.6f} to {points[:, 1].max():.6f}")
        print(f"  Z: {points[:, 2].min():.6f} to {points[:, 2].max():.6f}")
        
        # Check for colors (intensity information)
        if pcd.has_colors():
            colors = np.asarray(pcd.colors)
            print(f"\nColor information:")
            print(f"  Has colors: Yes")
            print(f"  Color array shape: {colors.shape}")
            print(f"  Color range: {colors.min():.6f} to {colors.max():.6f}")
        else:
            print(f"\nColor information: No colors")
        
        # Sample points
        print(f"\nSample points (first 5):")
        for i in range(min(5, len(points))):
            if pcd.has_colors():
                color = np.asarray(pcd.colors)[i]
                print(f"  Point {i}: X={points[i, 0]:.6f}, Y={points[i, 1]:.6f}, Z={points[i, 2]:.6f}, "
                      f"Color=({color[0]:.3f}, {color[1]:.3f}, {color[2]:.3f})")
            else:
                print(f"  Point {i}: X={points[i, 0]:.6f}, Y={points[i, 1]:.6f}, Z={points[i, 2]:.6f}")
        
        print(f"\n✓ PCD file verification successful!")
        return True
        
    except Exception as e:
        print(f"✗ Error reading PCD file: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    pcd_file_path = r"C:\Users\<USER>\Desktop\24052025\only_las_files\HT412_1738935654_3217135_1422974157258029_1422974185645127.pcd"
    verify_pcd_file(pcd_file_path)

if __name__ == "__main__":
    main()
